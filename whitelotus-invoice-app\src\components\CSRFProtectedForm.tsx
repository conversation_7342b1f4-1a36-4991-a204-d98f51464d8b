'use client';

import React, { FormEvent, ReactNode } from 'react';
import { useCSRFToken } from '@/hooks/useCSRF';

interface CSRFProtectedFormProps {
  children: ReactNode;
  onSubmit: (event: FormEvent<HTMLFormElement>, csrfToken: string) => void | Promise<void>;
  className?: string;
  method?: 'POST' | 'PUT' | 'DELETE';
  action?: string;
}

/**
 * Form component that automatically includes CSRF protection
 */
export function CSRFProtectedForm({
  children,
  onSubmit,
  className,
  method = 'POST',
  action,
}: CSRFProtectedFormProps) {
  const csrfToken = useCSRFToken();

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!csrfToken) {
      console.error('CSRF token not available');
      alert('Security token not available. Please refresh the page and try again.');
      return;
    }

    try {
      await onSubmit(event, csrfToken);
    } catch (error) {
      console.error('Form submission error:', error);
      
      // Check if it's a CSRF error
      if (error instanceof Error && error.message.includes('CSRF')) {
        alert('Security token expired. Please refresh the page and try again.');
      } else {
        throw error; // Re-throw non-CSRF errors
      }
    }
  };

  if (!csrfToken) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
        <p className="text-yellow-800">Loading security token...</p>
      </div>
    );
  }

  return (
    <form
      onSubmit={handleSubmit}
      className={className}
      method={method}
      action={action}
    >
      {/* Hidden CSRF token field for non-JS fallback */}
      <input
        type="hidden"
        name="csrfToken"
        value={csrfToken}
      />
      {children}
    </form>
  );
}

/**
 * Hook to get CSRF token for manual form handling
 */
export function useCSRFFormData(): { csrfToken: string | null; addCSRFToFormData: (formData: FormData) => FormData } {
  const csrfToken = useCSRFToken();

  const addCSRFToFormData = (formData: FormData): FormData => {
    if (csrfToken) {
      formData.append('csrfToken', csrfToken);
    }
    return formData;
  };

  return {
    csrfToken,
    addCSRFToFormData,
  };
}

/**
 * Utility function to add CSRF token to request body
 */
export function addCSRFToRequestBody(body: any, csrfToken: string): any {
  if (typeof body === 'object' && body !== null) {
    return {
      ...body,
      csrfToken,
    };
  }
  return body;
}
