import { NextRequest, NextResponse } from 'next/server';

/**
 * Simple API route handler for testing
 */

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'API is working',
    timestamp: new Date().toISOString()
  });
}

export async function POST(request: NextRequest) {
  return NextResponse.json({
    message: 'POST request received',
    timestamp: new Date().toISOString()
  });
}
