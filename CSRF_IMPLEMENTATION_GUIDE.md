# CSRF Protection Implementation Guide

This guide explains the comprehensive CSRF (Cross-Site Request Forgery) protection implementation for both the frontend (Next.js) and backend (Express.js) applications.

## Overview

CSRF protection has been implemented with the following features:
- Automatic token generation and validation
- Configurable security settings
- Easy-to-use React components and hooks
- Comprehensive error handling
- Production-ready configuration

## Backend Implementation

### 1. CSRF Middleware (`whitelotus_assignment/src/middlewares/csrfMiddleware.ts`)

The backend provides two main middleware functions:

#### `generateCSRFMiddleware`
- Generates CSRF tokens for authenticated users
- Stores tokens with expiration in memory (can be extended to use Redis)
- Sets secure HTTP-only cookies

#### `validateCSRFMiddleware`
- Validates CSRF tokens for state-changing operations (POST, PUT, DELETE)
- Uses timing-safe comparison to prevent timing attacks
- Provides detailed error messages in development

### 2. CSRF Routes (`whitelotus_assignment/src/routes/csrfRoute.ts`)

- `GET /api/csrf-token` - Generates and returns CSRF token
- `POST /api/csrf-token/validate` - Validates CSRF token (for testing)

### 3. Configuration (`whitelotus_assignment/src/config/csrfConfig.ts`)

Centralized configuration with environment variable support:

```typescript
// Environment variables you can set:
CSRF_TOKEN_LENGTH=32
CSRF_TOKEN_EXPIRY=3600000  // 1 hour in milliseconds
CSRF_HEADER_NAME=x-csrf-token
CSRF_COOKIE_NAME=csrf-token
CSRF_SECURE_COOKIES=true
CSRF_SAME_SITE=strict
CSRF_HTTP_ONLY=true
```

### 4. Integration

CSRF protection is integrated into protected routes:

```typescript
// Example: Invoice creation route with CSRF protection
router.post(
    '/invoice',
    sensitiveRouteRateLimiter,
    validateCSRFMiddleware, // CSRF protection
    fieldsValidator,
    invoice.createInvoice
);
```

## Frontend Implementation

### 1. CSRF Token Manager (`whitelotus-invoice-app/src/lib/security.ts`)

Automatic token management with:
- Token caching and expiration handling
- Automatic token refresh
- Error handling with custom CSRF errors

### 2. API Client Integration (`whitelotus-invoice-app/src/lib/api.ts`)

The API client automatically:
- Includes CSRF tokens in state-changing requests
- Handles CSRF token errors
- Refreshes tokens when needed

### 3. React Hooks (`whitelotus-invoice-app/src/hooks/useCSRF.ts`)

#### `useCSRF()`
Complete CSRF token management:
```typescript
const { token, isLoading, error, refreshToken, clearToken } = useCSRF();
```

#### `useCSRFToken()`
Simple token access:
```typescript
const csrfToken = useCSRFToken();
```

### 4. Protected Form Component (`whitelotus-invoice-app/src/components/CSRFProtectedForm.tsx`)

Easy-to-use form component with automatic CSRF protection:

```typescript
<CSRFProtectedForm
  onSubmit={async (event, csrfToken) => {
    // Form submission with CSRF token
    const formData = new FormData(event.currentTarget);
    // csrfToken is automatically included
  }}
  className="space-y-4"
>
  <input type="text" name="client_name" required />
  <input type="number" name="amount" required />
  <button type="submit">Create Invoice</button>
</CSRFProtectedForm>
```

### 5. Error Handling (`whitelotus-invoice-app/src/lib/errors/CSRFError.ts`)

Comprehensive error handling with:
- Custom CSRF error types
- User-friendly error messages
- Automatic retry suggestions

## Usage Examples

### Backend: Protecting a Route

```typescript
import { validateCSRFMiddleware } from '../middlewares/csrfMiddleware';

router.post('/api/sensitive-action', 
  tokenHandler,              // Authentication
  validateCSRFMiddleware,    // CSRF protection
  (req, res) => {
    // Your route handler
  }
);
```

### Frontend: Using CSRF in Forms

#### Option 1: Using CSRFProtectedForm Component

```typescript
import { CSRFProtectedForm } from '@/components/CSRFProtectedForm';

function MyForm() {
  const handleSubmit = async (event, csrfToken) => {
    const formData = new FormData(event.currentTarget);
    
    // Make API call - CSRF token is automatically included by API client
    await apiClient.post('/api/create-invoice', {
      client_name: formData.get('client_name'),
      amount: Number(formData.get('amount')),
    });
  };

  return (
    <CSRFProtectedForm onSubmit={handleSubmit}>
      <input type="text" name="client_name" required />
      <input type="number" name="amount" required />
      <button type="submit">Submit</button>
    </CSRFProtectedForm>
  );
}
```

#### Option 2: Manual CSRF Token Handling

```typescript
import { useCSRFToken } from '@/hooks/useCSRF';

function MyComponent() {
  const csrfToken = useCSRFToken();

  const handleAction = async () => {
    if (!csrfToken) {
      alert('Security token not available');
      return;
    }

    // API client automatically includes CSRF token
    await apiClient.post('/api/action', { data: 'value' });
  };

  return <button onClick={handleAction}>Perform Action</button>;
}
```

## Security Features

1. **Cryptographically Secure Tokens**: Uses `crypto.randomBytes()` for token generation
2. **Timing Attack Protection**: Uses `crypto.timingSafeEqual()` for token comparison
3. **Automatic Expiration**: Tokens expire after configurable time (default: 1 hour)
4. **Secure Cookies**: HTTP-only, secure, SameSite=strict cookies
5. **Memory Management**: Automatic cleanup of expired tokens
6. **Rate Limiting**: Integrated with existing rate limiting middleware

## Configuration Options

### Backend Environment Variables

```bash
# Token settings
CSRF_TOKEN_LENGTH=32
CSRF_TOKEN_EXPIRY=3600000

# Security settings
CSRF_SECURE_COOKIES=true
CSRF_SAME_SITE=strict
CSRF_HTTP_ONLY=true

# Headers and cookies
CSRF_HEADER_NAME=x-csrf-token
CSRF_COOKIE_NAME=csrf-token

# Maintenance
CSRF_CLEANUP_INTERVAL=300000

# Error handling
CSRF_DETAILED_ERRORS=false
CSRF_LOG_FAILURES=true
```

## Testing

### Backend Testing

```bash
# Get CSRF token
curl -X GET http://localhost:3000/api/csrf-token \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -c cookies.txt

# Use CSRF token in request
curl -X POST http://localhost:3000/api/auth/invoice \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "x-csrf-token: YOUR_CSRF_TOKEN" \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d '{"client_name":"Test","amount":100,"status":"PENDING","due_date":"2024-12-31","invoice_file_name":"test.pdf"}'
```

### Frontend Testing

The frontend automatically handles CSRF tokens, but you can test manually:

```typescript
// Test CSRF token fetching
import { csrfTokenManager } from '@/lib/security';

const token = await csrfTokenManager.getToken();
console.log('CSRF Token:', token);
```

## Migration Guide

### For Existing Forms

1. Replace regular `<form>` with `<CSRFProtectedForm>`
2. Update form submission handlers to receive `csrfToken` parameter
3. Remove manual CSRF token handling (API client handles it automatically)

### For Existing API Calls

No changes needed! The API client automatically includes CSRF tokens for state-changing operations.

## Troubleshooting

### Common Issues

1. **"CSRF token not found"**: Ensure user is authenticated before accessing CSRF-protected routes
2. **"Invalid CSRF token"**: Token may have expired, refresh the page
3. **"Security token not available"**: Check network connectivity and authentication status

### Debug Mode

Set `CSRF_DETAILED_ERRORS=true` in development for detailed error messages.

## Production Considerations

1. **Use Redis**: Replace in-memory token storage with Redis for scalability
2. **Monitor Performance**: CSRF validation adds minimal overhead but monitor in high-traffic scenarios
3. **Log Security Events**: Enable CSRF failure logging for security monitoring
4. **Regular Token Rotation**: Consider shorter token expiry times for high-security applications

This implementation provides robust CSRF protection while maintaining ease of use for developers.
