# Render Infrastructure as Code Configuration
# This file defines all services for the WhiteLotus Invoice application

databases:
  - name: whitelotus-invoice-db
    databaseName: whitelotus_invoice_db
    user: whitelotus_invoice_db_user
    plan: free
    region: oregon

services:
  # Backend API Service
  - type: web
    name: whitelotus-backend-api
    runtime: node
    plan: free
    region: oregon
    buildCommand: cd whitelotus_assignment && npm install && npm run build
    startCommand: cd whitelotus_assignment && npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: DATABASE_NAME
        fromDatabase:
          name: whitelotus-invoice-db
          property: database
      - key: DATABASE_USER
        fromDatabase:
          name: whitelotus-invoice-db
          property: user
      - key: DATABASE_HOST
        fromDatabase:
          name: whitelotus-invoice-db
          property: host
      - key: DATABASE_PORT
        fromDatabase:
          name: whitelotus-invoice-db
          property: port
      - key: DATABASE_PASS
        fromDatabase:
          name: whitelotus-invoice-db
          property: password
      - key: ENCRYPTION_KEY
        generateValue: true
      - key: USESERVER
        value: true
      - key: MODE
        value: production

  # Frontend Static Site
  - type: web
    name: whitelotus-frontend
    runtime: node
    plan: free
    region: oregon
    buildCommand: cd whitelotus-invoice-app && npm install && npm run build
    startCommand: cd whitelotus-invoice-app && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: NEXT_PUBLIC_API_URL
        fromService:
          type: web
          name: whitelotus-backend-api
          property: host
      - key: JWT_SECRET
        generateValue: true
      - key: COOKIE_NAME
        value: auth-token
      - key: COOKIE_MAX_AGE
        value: 604800
      - key: ADMIN_EMAIL
        value: <EMAIL>
      - key: ADMIN_PASSWORD
        value: Pass@1234
      - key: PAGINATION_DEFAULT_LIMIT
        value: 5
      - key: PAGINATION_MAX_LIMIT
        value: 100

# Alternative Static Site Configuration
# Uncomment this section if you prefer to deploy frontend as static site
# - type: static
#   name: whitelotus-frontend-static
#   buildCommand: cd whitelotus-invoice-app && npm install && npm run build && npm run export
#   staticPublishPath: whitelotus-invoice-app/out
#   envVars:
#     - key: NODE_ENV
#       value: production
#     - key: NEXT_PUBLIC_API_URL
#       fromService:
#         type: web
#         name: whitelotus-backend-api
#         property: host
