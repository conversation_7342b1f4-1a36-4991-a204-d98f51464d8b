import { NextFunction, Request, Response } from 'express';
import models from '../models';
import { TOKEN, httpStatusCodes } from '../utils/constants';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { logger } from '../utils/logger';

class User {
    /**
     * @swagger
     * /login:
     *   post:
     *     tags:
     *       - Authentication
     *     summary: User login
     *     description: Authenticate user with email and password
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/LoginRequest'
     *           example:
     *             email: <EMAIL>
     *             password: Pass@1234
     *     responses:
     *       200:
     *         description: Login successful
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/LoginResponse'
     *       400:
     *         description: Invalid credentials or user blocked
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ErrorResponse'
     *             examples:
     *               userNotFound:
     *                 summary: User not found
     *                 value:
     *                   status: 400
     *                   message: User not found
     *                   error: {}
     *               userBlocked:
     *                 summary: User blocked
     *                 value:
     *                   status: 400
     *                   message: User is blocked, please contact administrator.!!!
     *                   error: {}
     *               incorrectPassword:
     *                 summary: Incorrect password
     *                 value:
     *                   status: 400
     *                   message: Incorrect password
     *                   error: {}
     *       500:
     *         description: Internal server error
     *         content:
     *           application/json:
     *             schema:
     *               $ref: '#/components/schemas/ErrorResponse'
     */
    async loginUser(req: Request, res: Response, next: NextFunction) {
        try {
            const { email, password } = req.body;

            const user = await models.users.findOne({
                where: { email }
            });

            if (!user) {
                throw new Error('User not found');
            }

            if (user.is_blocked || !user.is_active) {
                throw new Error(`User is blocked, please contact administrator.!!!`);
            }

            const isPasswordMatched = await bcrypt.compare(password, user.password);

            if (!isPasswordMatched) {
                throw new Error('Incorrect password');
            }

            const authToken = await jwt.sign({ id: user.id, role: 'user' }, TOKEN);

            /// remove password from response
            const { password: _, ...rest } = JSON.parse(JSON.stringify(user));

            res.send({
                status: httpStatusCodes.SUCCESS_CODE,
                message: `User loggedIn successfully`,
                data: { ...rest, token: authToken }
            });

            ///
        } catch (error: any) {
            logger.error(error);
            next(error);
        }
    }
}

export default new User();
