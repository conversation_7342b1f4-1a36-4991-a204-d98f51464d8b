import { NextRequest, NextResponse } from 'next/server';
import { RouteHandler, RouteContext } from './types';

/**
 * Compatibility layer for migrating existing Next.js route handlers
 * to the new centralized route system
 */

/**
 * Convert a Next.js App Router route handler to our route handler format
 */
export function adaptNextJsHandler(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>
): RouteHandler {
  return async (request: NextRequest, context?: RouteContext): Promise<NextResponse> => {
    // Convert our RouteContext to Next.js context format
    const nextJsContext = context?.params ? { params: Promise.resolve(context.params) } : undefined;
    return await handler(request, nextJsContext);
  };
}

/**
 * Create a route handler that works with both old and new systems
 */
export function createCompatibleHandler(
  handler: RouteHandler
): (request: NextRequest, context?: any) => Promise<NextResponse> {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    // Convert Next.js context to our RouteContext format
    let routeContext: RouteContext | undefined;
    
    if (context) {
      const params = context.params ? await context.params : undefined;
      const url = new URL(request.url);
      
      routeContext = {
        params,
        searchParams: url.searchParams,
      };
    }
    
    return await handler(request, routeContext);
  };
}

/**
 * Wrapper for existing route files to use the new system
 */
export function wrapExistingRoute(
  method: string,
  path: string,
  handler: RouteHandler,
  options: {
    requiresAuth?: boolean;
    rateLimit?: { requests: number; windowMs: number };
    description?: string;
    tags?: string[];
  } = {}
) {
  // This can be used to register existing routes with the new system
  return {
    method: method.toUpperCase(),
    path,
    handler,
    requiresAuth: options.requiresAuth || false,
    rateLimit: options.rateLimit,
    description: options.description || `${method.toUpperCase()} ${path}`,
    tags: options.tags || ['legacy'],
  };
}

/**
 * Migration helper to gradually move routes to the new system
 */
export class RouteMigrationHelper {
  private migratedRoutes = new Set<string>();
  
  /**
   * Mark a route as migrated to the new system
   */
  markAsMigrated(method: string, path: string): void {
    const key = `${method.toUpperCase()}:${path}`;
    this.migratedRoutes.add(key);
  }
  
  /**
   * Check if a route has been migrated
   */
  isMigrated(method: string, path: string): boolean {
    const key = `${method.toUpperCase()}:${path}`;
    return this.migratedRoutes.has(key);
  }
  
  /**
   * Get all migrated routes
   */
  getMigratedRoutes(): string[] {
    return Array.from(this.migratedRoutes);
  }
  
  /**
   * Get migration status
   */
  getMigrationStatus() {
    return {
      migratedCount: this.migratedRoutes.size,
      migratedRoutes: this.getMigratedRoutes(),
    };
  }
}

// Global migration helper instance
export const migrationHelper = new RouteMigrationHelper();

/**
 * Decorator for route handlers to automatically register them
 */
export function registerRoute(
  method: string,
  path: string,
  options: {
    requiresAuth?: boolean;
    rateLimit?: { requests: number; windowMs: number };
    description?: string;
    tags?: string[];
  } = {}
) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalHandler = descriptor.value;
    
    // Mark as migrated
    migrationHelper.markAsMigrated(method, path);
    
    // Return the original handler (for backward compatibility)
    return descriptor;
  };
}

/**
 * Helper to create route handlers that work in both systems
 */
export function createDualCompatibleHandler(
  handler: RouteHandler,
  options: {
    requiresAuth?: boolean;
    rateLimit?: { requests: number; windowMs: number };
    description?: string;
    tags?: string[];
  } = {}
) {
  const compatibleHandler = createCompatibleHandler(handler);
  
  // Add metadata for the new system
  (compatibleHandler as any).__routeOptions = options;
  
  return compatibleHandler;
}

/**
 * Extract route options from a compatible handler
 */
export function extractRouteOptions(handler: any) {
  return handler.__routeOptions || {};
}
