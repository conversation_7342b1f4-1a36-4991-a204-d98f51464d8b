import { NextRequest, NextResponse } from 'next/server';
import { createToken, createAuth<PERSON><PERSON>ie, getCurrentUserFromRequest } from '@/lib/auth';
import { checkRateLimit, validateEmail, getSecurityHeaders } from '@/lib/security';
import { DEMO_CREDENTIALS, SECURITY_CONFIG } from '@/lib/config';
import { LoginRequest, User } from '@/types';

/**
 * Authentication route handlers
 */
export const authHandlers = {
  /**
   * POST /auth/login - User login
   */
  async login(request: NextRequest): Promise<NextResponse> {
    try {
      // Rate limiting using environment-configured values
      const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
      if (!checkRateLimit(
        `login:${clientIP}`,
        SECURITY_CONFIG.RATE_LIMIT.LOGIN_ATTEMPTS,
        SECURITY_CONFIG.RATE_LIMIT.LOGIN_WINDOW
      )) {
        return NextResponse.json(
          { status: 429, message: 'Too many login attempts. Please try again later.' },
          { status: 429, headers: getSecurityHeaders() }
        );
      }

      const body: LoginRequest = await request.json();
      const { email, password } = body;

      // Validate input
      if (!email || !password) {
        return NextResponse.json(
          { status: 400, message: 'Email and password are required' },
          { status: 400, headers: getSecurityHeaders() }
        );
      }

      if (!validateEmail(email)) {
        return NextResponse.json(
          { status: 400, message: 'Invalid email format' },
          { status: 400, headers: getSecurityHeaders() }
        );
      }

      // Check credentials against environment-configured demo values
      if (email !== DEMO_CREDENTIALS.EMAIL || password !== DEMO_CREDENTIALS.PASSWORD) {
        return NextResponse.json(
          { status: 400, message: 'Invalid credentials' },
          { status: 400, headers: getSecurityHeaders() }
        );
      }

      // Create user object (mock data based on API documentation)
      const user: User = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        first_name: 'Admin',
        last_name: 'Fintech',
        email: email,
        is_active: true,
        token: '', // Will be set below
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: new Date().toISOString(),
      };

      // Create JWT token
      const token = await createToken({ user });
      user.token = token;

      // Create response
      const response = NextResponse.json({
        status: 200,
        message: 'User loggedIn successfully',
        data: user,
      }, {
        status: 200,
        headers: getSecurityHeaders(),
      });

      // Set auth cookie
      const authCookie = createAuthCookie(token);
      response.cookies.set(authCookie);

      return response;
    } catch (error) {
      console.error('Login error:', error);
      return NextResponse.json(
        { status: 500, message: 'Internal server error' },
        { status: 500, headers: getSecurityHeaders() }
      );
    }
  },

  /**
   * POST /auth?action=logout - User logout
   */
  async logout(request: NextRequest): Promise<NextResponse> {
    try {
      // Create response
      const response = NextResponse.json({
        status: 200,
        message: 'User logged out successfully',
      }, {
        status: 200,
        headers: getSecurityHeaders(),
      });

      // Clear auth cookie
      response.cookies.set('auth-token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 0, // Expire immediately
        path: '/',
      });

      return response;
    } catch (error) {
      console.error('Logout error:', error);
      return NextResponse.json(
        { status: 500, message: 'Internal server error' },
        { status: 500, headers: getSecurityHeaders() }
      );
    }
  },

  /**
   * GET /auth?action=me - Get current user info
   */
  async me(request: NextRequest): Promise<NextResponse> {
    try {
      // Check authentication
      const user = await getCurrentUserFromRequest(request);
      if (!user) {
        return NextResponse.json(
          { status: 401, message: 'Unauthorized' },
          { status: 401, headers: getSecurityHeaders() }
        );
      }

      // Rate limiting
      const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
      if (!checkRateLimit(`user-info:${clientIP}`, 30, 60000)) {
        return NextResponse.json(
          { status: 429, message: 'Too many requests' },
          { status: 429, headers: getSecurityHeaders() }
        );
      }

      return NextResponse.json({
        status: 200,
        message: 'User information retrieved successfully',
        data: user,
      }, {
        status: 200,
        headers: getSecurityHeaders(),
      });
    } catch (error) {
      console.error('Get user info error:', error);
      return NextResponse.json(
        { status: 500, message: 'Internal server error' },
        { status: 500, headers: getSecurityHeaders() }
      );
    }
  },
};
