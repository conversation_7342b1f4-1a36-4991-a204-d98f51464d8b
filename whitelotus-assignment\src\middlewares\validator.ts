import { NextFunction, Request, Response } from 'express';
import { httpStatusCodes } from '../utils/constants';

export const fieldsValidator = async (req: Request, res: Response, next: NextFunction, schema: any) => {
    try {
        await schema.validateAsync(req.body);
        next();
    } catch (error: any) {
        res.status(httpStatusCodes.SERVER_ERROR_CODE).json({
            status: httpStatusCodes.SERVER_ERROR_CODE,
            message: typeof error === 'string' ? error : typeof error.message === 'string' ? error.message : 500
        });
    }
};
