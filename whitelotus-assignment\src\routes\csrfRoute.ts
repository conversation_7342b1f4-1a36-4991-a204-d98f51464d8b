import { Request, Response, Router } from 'express';
import { getCSRFToken, generateCSRFMiddleware } from '../middlewares/csrfMiddleware';
import { httpStatusCodes } from '../utils/constants';
import { logger } from '../utils/logger';

const router = Router();

/**
 * GET /csrf-token - Get CSRF token for authenticated user
 * This endpoint provides CSRF tokens for authenticated users
 */
router.get('/csrf-token', generateCSRFMiddleware, (req: Request, res: Response) => {
    try {
        // Get the CSRF token from response locals (set by generateCSRFMiddleware)
        const csrfToken = res.locals.csrfToken;

        if (!csrfToken) {
            logger.error('CSRF token not found in response locals');
            return res.status(httpStatusCodes.SERVER_ERROR_CODE).json({
                status: httpStatusCodes.SERVER_ERROR_CODE,
                message: 'Failed to generate CSRF token'
            });
        }

        // Return the token
        res.status(httpStatusCodes.SUCCESS_CODE).json({
            status: httpStatusCodes.SUCCESS_CODE,
            message: 'CSRF token generated successfully',
            data: {
                csrfToken,
                expiresIn: 3600 // 1 hour in seconds
            }
        });
    } catch (error) {
        logger.error('Error in CSRF token endpoint:', error);
        res.status(httpStatusCodes.SERVER_ERROR_CODE).json({
            status: httpStatusCodes.SERVER_ERROR_CODE,
            message: 'Internal server error'
        });
    }
});

/**
 * POST /csrf-token/validate - Validate CSRF token (for testing purposes)
 * This endpoint can be used to test CSRF token validation
 */
router.post('/csrf-token/validate', (req: Request, res: Response) => {
    try {
        // If we reach here, CSRF validation passed (handled by middleware)
        res.status(httpStatusCodes.SUCCESS_CODE).json({
            status: httpStatusCodes.SUCCESS_CODE,
            message: 'CSRF token is valid'
        });
    } catch (error) {
        logger.error('Error in CSRF validation endpoint:', error);
        res.status(httpStatusCodes.SERVER_ERROR_CODE).json({
            status: httpStatusCodes.SERVER_ERROR_CODE,
            message: 'Internal server error'
        });
    }
});

export default router;
