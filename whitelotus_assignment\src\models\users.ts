import { Sequelize, UUIDV4, Model, Optional, BuildOptions } from 'sequelize';
import bcrypt from 'bcrypt';
import { logger } from '../utils/logger';

export interface UserAttributes {
    id: string; // id is an auto-generated UUID
    first_name: string;
    last_name: string;
    email: string;
    password: string;
    _deleted: boolean;
    is_active: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

interface UserCreationAttributes extends Optional<UserAttributes, 'id'> {}

interface UserInstance extends Model<UserAttributes, UserCreationAttributes>, UserAttributes {
    createdAt?: Date;
    updatedAt?: Date;
}

type UserStatic = typeof Model & { associate: (models: any) => void } & (new (
        values?: Record<string, unknown>,
        options?: BuildOptions
    ) => UserInstance);

export default (sequelize: Sequelize, DataTypes: any) => {
    const users = sequelize.define<UserInstance>(
        'users',
        {
            id: {
                type: DataTypes.UUID,
                allowNull: false,
                primaryKey: true,
                defaultValue: UUIDV4
            },
            first_name: {
                type: DataTypes.STRING,
                allowNull: false
            },
            last_name: {
                type: DataTypes.STRING,
                allowNull: false
            },
            email: {
                type: DataTypes.STRING,
                allowNull: false
            },
            password: {
                type: DataTypes.STRING,
                allowNull: false
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            _deleted: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            }
        },
        {
            freezeTableName: true,
            defaultScope: {
                where: {
                    _deleted: false
                }
            }
        }
    ) as UserStatic;

    //
    // await users.sync({ alter: true })

    // Sync and add default entry
    (async () => {
        try {
            // if no user exist create default admin user
            const adminUser = await users.findOne({
                where: {
                    email: '<EMAIL>'
                }
            });

            // create default user
            if (!adminUser) {
                // encrpyt pass using bc crypt
                const hashedPassword = await bcrypt.hash('Pass@1234', 10);

                await users.create({
                    first_name: 'Admin',
                    last_name: 'Fintech',
                    email: '<EMAIL>',
                    password: hashedPassword,
                    _deleted: false,
                    is_active: true
                });
            }
        } catch (error) {
            logger.error('❌ Error syncing DB:', error);
        }
    })();

    return users;
};
