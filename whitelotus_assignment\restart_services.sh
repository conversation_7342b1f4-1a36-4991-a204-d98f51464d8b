#!/bin/bash

# Restart PostgreSQL service
echo "Restarting PostgreSQL..."
sudo systemctl restart postgresql

# Check if PostgreSQL restarted successfully
if systemctl is-active --quiet postgresql; then
    echo "PostgreSQL restarted successfully."
else
    echo "Failed to restart PostgreSQL." >&2
    exit 1
fi

# Restart all PM2 processes
echo "Restarting PM2 services..."
pm2 restart all

# Save PM2 process list (optional, but good practice)
pm2 save

echo "All PM2 services restarted successfully."