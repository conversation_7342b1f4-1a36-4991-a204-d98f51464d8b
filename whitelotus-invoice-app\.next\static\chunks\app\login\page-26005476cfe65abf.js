(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{1038:(e,r,a)=>{Promise.resolve().then(a.bind(a,1458))},1458:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>i});var s=a(5155),n=a(2115),t=a(1920),l=a(6895);function o(e){let{children:r,onSubmit:a,className:t,method:o="POST",action:i}=e,d=function(){let{token:e}=function(){let[e,r]=(0,n.useState)(null),[a,s]=(0,n.useState)(!1),[t,o]=(0,n.useState)(null),i=(0,n.useCallback)(async()=>{s(!0),o(null);try{let e=await l.Hi.getToken();r(e)}catch(e){o(e instanceof Error?e.message:"Failed to fetch CSRF token"),console.error("CSRF token fetch error:",e)}finally{s(!1)}},[]),d=(0,n.useCallback)(async()=>{l.Hi.clearToken(),await i()},[i]),c=(0,n.useCallback)(()=>{l.Hi.clearToken(),r(null),o(null)},[]);return(0,n.useEffect)(()=>{i()},[i]),{token:e,isLoading:a,error:t,refreshToken:d,clearToken:c}}();return e}(),c=async e=>{if(e.preventDefault(),!d){console.error("CSRF token not available"),alert("Security token not available. Please refresh the page and try again.");return}try{await a(e,d)}catch(e){if(console.error("Form submission error:",e),e instanceof Error&&e.message.includes("CSRF"))alert("Security token expired. Please refresh the page and try again.");else throw e}};return d?(0,s.jsxs)("form",{onSubmit:c,className:t,method:o,action:i,children:[(0,s.jsx)("input",{type:"hidden",name:"csrfToken",value:d}),r]}):(0,s.jsx)("div",{className:"p-4 bg-yellow-50 border border-yellow-200 rounded-md",children:(0,s.jsx)("p",{className:"text-yellow-800",children:"Loading security token..."})})}function i(){let[e,r]=(0,n.useState)(""),[a,l]=(0,n.useState)(""),[i,d]=(0,n.useState)(""),[c,u]=(0,n.useState)(!1),{login:m}=(0,t.A)(),p=async(r,s)=>{u(!0),d("");try{await m(e,a)}catch(e){d(e instanceof Error?e.message:"Login failed")}finally{u(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),(0,s.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Demo credentials: <EMAIL> / password123"})]}),(0,s.jsxs)(o,{onSubmit:p,className:"mt-8 space-y-6",children:[(0,s.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>r(e.target.value),className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm",placeholder:"Email address"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:a,onChange:e=>l(e.target.value),className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm",placeholder:"Password"})]})]}),i&&(0,s.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,s.jsx)("div",{className:"text-sm text-red-700",children:i})}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:c,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed",children:c?"Signing in...":"Sign in"})})]})]})})}}},e=>{e.O(0,[586,920,441,255,358],()=>e(e.s=1038)),_N_E=e.O()}]);