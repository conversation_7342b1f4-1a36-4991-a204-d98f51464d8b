import { NextRequest, NextResponse } from 'next/server';

/**
 * HTTP Methods supported by the API
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'OPTIONS' | 'HEAD';

/**
 * Route handler function signature
 */
export type RouteHandler = (
  request: NextRequest,
  context?: RouteContext
) => Promise<NextResponse>;

/**
 * Route context containing parameters and additional data
 */
export interface RouteContext {
  params?: Record<string, string>;
  searchParams?: URLSearchParams;
  [key: string]: any;
}

/**
 * Route definition structure
 */
export interface RouteDefinition {
  /** Route path pattern (e.g., '/auth/login', '/auth/invoice/:id') */
  path: string;
  /** HTTP method */
  method: HttpMethod;
  /** Route handler function */
  handler: RouteHandler;
  /** Whether authentication is required */
  requiresAuth?: boolean;
  /** Rate limiting configuration */
  rateLimit?: {
    requests: number;
    windowMs: number;
    keyGenerator?: (request: NextRequest) => string;
  };
  /** Route description for documentation */
  description?: string;
  /** Route tags for grouping */
  tags?: string[];
}

/**
 * Route registry structure
 */
export interface RouteRegistry {
  [key: string]: RouteDefinition;
}

/**
 * Route matcher result
 */
export interface RouteMatch {
  definition: RouteDefinition;
  params: Record<string, string>;
}

/**
 * API Response structure
 */
export interface ApiResponse<T = any> {
  status: number;
  message: string;
  data?: T;
  error?: string;
  pagination?: {
    skip: number;
    limit: number;
    total: number;
  };
}

/**
 * Route middleware function signature
 */
export type RouteMiddleware = (
  request: NextRequest,
  context: RouteContext,
  next: () => Promise<NextResponse>
) => Promise<NextResponse>;

/**
 * Route configuration options
 */
export interface RouteConfig {
  /** Global middlewares to apply to all routes */
  globalMiddlewares?: RouteMiddleware[];
  /** Default rate limiting configuration */
  defaultRateLimit?: {
    requests: number;
    windowMs: number;
  };
  /** Default security headers */
  defaultHeaders?: Record<string, string>;
  /** Enable request/response logging */
  enableLogging?: boolean;
}
