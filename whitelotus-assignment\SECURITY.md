# Security Implementation

This document outlines the security features implemented in the Whitelotus Assignment API.

## 🛡️ Security Headers (Helmet)

The application now uses Helmet middleware to set various HTTP security headers:

### Implemented Headers

1. **Content Security Policy (CSP)**
   - Prevents XSS attacks by controlling resource loading
   - Configured to allow self-hosted resources and specific trusted domains
   - Blocks inline scripts and styles (except where explicitly allowed)

2. **X-Content-Type-Options**
   - Set to `nosniff` to prevent MIME type sniffing attacks

3. **X-Frame-Options**
   - Set to `DENY` to prevent clickjacking attacks

4. **X-XSS-Protection**
   - Enables browser's built-in XSS protection

5. **Strict-Transport-Security (HSTS)**
   - Forces HTTPS connections (when deployed with HTTPS)
   - Set to 1 year with includeSubDomains and preload

6. **X-Powered-By Header Removal**
   - Removes the default Express.js header to reduce information disclosure

### Configuration Location
- File: `src/app.ts`
- Applied globally to all routes

## 🚦 Rate Limiting

Three levels of rate limiting have been implemented:

### 1. Login Rate Limiter
- **Endpoint**: `/api/login`
- **Limit**: 5 attempts per 15 minutes per IP
- **Purpose**: Prevent brute force attacks on login
- **Response**: 429 status with descriptive message

### 2. Sensitive Route Rate Limiter
- **Endpoints**: `/api/auth/invoice` (POST)
- **Limit**: 20 requests per 15 minutes per IP
- **Purpose**: Protect resource-intensive operations
- **Response**: 429 status with descriptive message

### 3. General Rate Limiter
- **Scope**: All API endpoints
- **Limit**: 100 requests per 15 minutes per IP
- **Purpose**: Prevent API abuse and DoS attacks
- **Response**: 429 status with descriptive message

### Rate Limiting Features

- **IP-based tracking**: Each IP address is tracked separately
- **Trusted IP bypass**: Localhost IPs (127.0.0.1, ::1) are exempted
- **Standard headers**: Returns rate limit information in response headers
- **Logging**: Failed attempts are logged for monitoring
- **Graceful responses**: Returns JSON error messages instead of plain text

### Configuration Location
- File: `src/middlewares/rateLimiter.ts`
- Applied in respective route files

## 📁 File Structure

```
src/
├── middlewares/
│   ├── rateLimiter.ts      # Rate limiting configurations
│   └── index.ts            # Middleware exports
├── routes/
│   ├── unAuthRoute.ts      # Login route with rate limiting
│   └── authRoute.ts        # Protected routes with rate limiting
└── app.ts                  # Main app with Helmet configuration
```

## 🔧 Usage Examples

### Testing Rate Limits

You can test the rate limiting by making multiple requests:

```bash
# Test login rate limiting
for i in {1..6}; do
  curl -X POST http://localhost:3000/api/login \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"test"}'
  echo "Request $i completed"
done
```

### Checking Security Headers

```bash
curl -I http://localhost:3000/api-docs
```

## 🚀 Testing

A test script is provided to verify the security implementations:

```bash
node test-security.js
```

This script tests:
- Security headers presence
- Rate limiting functionality
- Rate limit headers

## 📊 Monitoring

### Rate Limit Logs

Rate limit violations are logged with the following information:
- IP address of the requester
- Route that was accessed
- Timestamp of the violation

### Log Locations

Check the application logs for entries like:
```
Rate limit exceeded for login attempt from IP: *************
Rate limit exceeded for sensitive route from IP: *************, Route: /api/auth/invoice
```

## 🔒 Security Best Practices Implemented

1. **Defense in Depth**: Multiple layers of security (headers + rate limiting)
2. **Principle of Least Privilege**: Different rate limits for different endpoints
3. **Fail Securely**: Rate limits return secure error messages
4. **Logging and Monitoring**: All security events are logged
5. **Configuration Management**: Security settings are centralized and configurable

## 🛠️ Dependencies Added

- `helmet`: ^7.1.0 - Security headers middleware
- `express-rate-limit`: ^7.1.5 - Rate limiting middleware
- `@types/express-rate-limit`: ^6.0.0 - TypeScript definitions

## 🔄 Future Enhancements

Consider implementing:
- Redis-based rate limiting for distributed systems
- JWT token rate limiting
- Geographic IP blocking
- Advanced CSP policies
- Security event alerting
- Rate limit bypass for authenticated premium users
