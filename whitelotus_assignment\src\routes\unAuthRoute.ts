import { NextFunction, Request, Response } from 'express';
import { fieldsValidator } from '../middlewares/validator';
import { loginUserSchema } from '../utils/schema/user_schema';
import { loginRateLimiter } from '../middlewares/rateLimiter';
import user from '../controllers/user';
import { Router } from 'express';
const router = Router();

/**
 * Total UnAuth Routes
 */
// login user
router.post(
    '/login',
    loginRateLimiter, // Apply rate limiting to login route
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, loginUserSchema),
    user.loginUser
);

export default router;
