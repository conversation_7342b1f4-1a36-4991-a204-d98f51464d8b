// =============================================================================
// CONFIGURATION - ALL VALUES NOW SOURCED FROM ENVIRONMENT VARIABLES
// =============================================================================

/**
 * API Configuration
 * All API-related settings including URLs, timeouts, and retry logic
 */
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
  TIMEOUT: parseInt(process.env.API_TIMEOUT || '10000', 10),
  RETRIES: parseInt(process.env.API_RETRIES || '3', 10),
} as const;

/**
 * Application Configuration
 * Core app settings including authentication, cookies, and pagination
 */
export const APP_CONFIG = {
  // Authentication settings
  JWT_SECRET: process.env.JWT_SECRET || (() => {
    console.warn('⚠️  WARNING: Using default JWT_SECRET. Set JWT_SECRET environment variable in production!');
    return 'your-secret-key';
  })(),
  COOKIE_NAME: process.env.COOKIE_NAME || 'auth-token',
  COOKIE_MAX_AGE: parseInt(process.env.COOKIE_MAX_AGE || '604800', 10), // 7 days in seconds

  // Pagination settings
  PAGINATION: {
    DEFAULT_LIMIT: parseInt(process.env.PAGINATION_DEFAULT_LIMIT || '5', 10),
    MAX_LIMIT: parseInt(process.env.PAGINATION_MAX_LIMIT || '100', 10),
  },

  // ISR (Incremental Static Regeneration) settings
  ISR_REVALIDATE: parseInt(process.env.ISR_REVALIDATE || '60', 10), // seconds
} as const;

/**
 * Security Configuration
 * Rate limiting and security-related settings
 */
export const SECURITY_CONFIG = {
  RATE_LIMIT: {
    LOGIN_ATTEMPTS: parseInt(process.env.RATE_LIMIT_LOGIN_ATTEMPTS || '5', 10),
    LOGIN_WINDOW: parseInt(process.env.RATE_LIMIT_LOGIN_WINDOW || '60000', 10), // 1 minute
    DOWNLOAD_ATTEMPTS: parseInt(process.env.RATE_LIMIT_DOWNLOAD_ATTEMPTS || '20', 10),
    DOWNLOAD_WINDOW: parseInt(process.env.RATE_LIMIT_DOWNLOAD_WINDOW || '60000', 10), // 1 minute
  },
} as const;

/**
 * Application Routes
 * All route definitions for the application
 */
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  INVOICE_DETAILS: '/invoice',
  CREATE_INVOICE: '/invoice/new',
} as const;

/**
 * API Routes
 * Authentication-related API endpoints
 */
export const AUTH_API = {
  LOGIN: '/auth/login',
  LOGOUT: '/auth/logout',
  ME: '/auth/me',
} as const;

/**
 * Invoice API Routes
 * Invoice-related API endpoints
 */
export const INVOICE_API = {
  LIST: '/invoices',
  CREATE: '/invoices',
  BY_ID: (id: string) => `/invoices/${id}`,
} as const;

/**
 * Media API Routes
 * File download and media-related endpoints
 */
export const MEDIA_API = {
  DOWNLOAD_INVOICE: (filename: string) => `/invoices/download/${filename}`,
} as const;

/**
 * Demo Credentials Configuration
 * WARNING: These are for demo purposes only. In production, use a proper
 * authentication system with a database instead of hardcoded credentials.
 */
export const DEMO_CREDENTIALS = {
  EMAIL: process.env.ADMIN_EMAIL || (() => {
    console.warn('⚠️  WARNING: Using default admin email. Set ADMIN_EMAIL environment variable!');
    return '<EMAIL>';
  })(),
  PASSWORD: process.env.ADMIN_PASSWORD || (() => {
    console.warn('⚠️  WARNING: Using default admin password. Set ADMIN_PASSWORD environment variable!');
    return 'Pass@1234';
  })(),
} as const;

/**
 * Development Configuration
 * Settings specific to development environment
 */
export const DEV_CONFIG = {
  DEBUG_LOGGING: process.env.DEBUG_LOGGING === 'true',
  PORT: parseInt(process.env.PORT || '3000', 10),
} as const;

// =============================================================================
// DEPRECATED: Legacy export for backward compatibility
// =============================================================================
/**
 * @deprecated Use DEMO_CREDENTIALS instead
 * This export is maintained for backward compatibility but will be removed in a future version
 */
export const HARDCODED_CREDENTIALS = DEMO_CREDENTIALS;
