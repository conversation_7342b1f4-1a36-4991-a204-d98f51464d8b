# Click2Cater Service API

E-carting API for invoice management and user authentication.

## 📚 API Documentation

The API documentation is generated from **inline JSDoc annotations** in controller files using **OpenAPI 3.0 (Swagger)**.

### Quick Access:
- **Local Swagger UI**: http://localhost:3000/api-docs (when server is running)
- **Generated OpenAPI JSON**: http://localhost:3000/api-docs.json
- **Generated OpenAPI YAML**: http://localhost:3000/api-docs.yaml

### Documentation Commands:
```bash
# View documentation locally
npm run dev
# Then visit: http://localhost:3000/api-docs

# Generate OpenAPI spec from annotations
npm run docs:generate

# Generate and serve documentation locally
npm run docs:serve
```

For detailed documentation setup and management instructions, see [API_DOCUMENTATION.md](./API_DOCUMENTATION.md).

## 🛡️ Security Features

This API implements comprehensive security measures:

- **Helmet Security Headers**: Protection against common web vulnerabilities
- **Rate Limiting**: Multi-tier rate limiting for different endpoints
- **Login Protection**: Brute force protection for authentication
- **API Abuse Prevention**: General rate limiting across all endpoints

For detailed security information, see [SECURITY.md](./SECURITY.md).

## 🚀 Getting Started

### Prerequisites
- Node.js (v14 or higher)
- PostgreSQL database
- npm or yarn

### Installation
```bash
# Install dependencies
npm install

# Build the project
npm run build

# Start development server
npm run dev
```

### Environment Variables
Create a `.env` file with the required environment variables (see `.env.example` if available).

## 📖 API Endpoints

### Authentication
- `POST /api/login` - User login

### Invoices (Authenticated)
- `GET /api/auth/invoice` - List invoices with pagination
- `POST /api/auth/invoice` - Create new invoice
- `GET /api/auth/invoice/:id` - Get invoice details

### Media
- `GET /invoice/:filename` - Download invoice file

## 🛠️ Development

```bash
# Development with hot reload
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run linting
npm run lint

# Format code
npm run prettier-all
```

## 📝 License

ISC License