{
    "compilerOptions": {
        "target": "es2017" /* Specify ECMAScript target version: 'ES3' (default), 'ES5', 'ES2015', 'ES2016', 'ES2017', 'ES2018', 'ES2019', 'ES2020', or 'ESNEXT'. */,
        "module": "commonjs" /* Specify module code generation: 'none', 'commonjs', 'amd', 'system', 'umd', 'es2015', 'es2020', or 'ESNext'. */,
        "lib": [
            "es2017",
            "es2017.object"
        ] /* Specify library files to be included in the compilation. */,
        "declaration": false /* Generates corresponding '.d.ts' file. */,
        "outDir": "./dist" /* Redirect output structure to the directory. */,
        "noEmitOnError": true,
        "strict": true /* Enable all strict type-checking options. */,
        "noImplicitAny": false /* Raise error on expressions and declarations with an implied 'any' type. */,
        "strictNullChecks": true /* Enable strict null checks. */,
        "noUnusedLocals": false /* Report errors on unused locals. */,
        "noUnusedParameters": false /* Report errors on unused parameters. */,
        "noFallthroughCasesInSwitch": true /* Report errors for fallthrough cases in switch statement. */,
        "moduleResolution": "node" /* Specify module resolution strategy: 'node' (Node.js) or 'classic' (TypeScript pre-1.6). */,
        "baseUrl": "./src" /* Base directory to resolve non-absolute module names. */,
        "esModuleInterop": true /* Enables emit interoperability between CommonJS and ES Modules via creation of namespace objects for all imports. Implies 'allowSyntheticDefaultImports'. */,
        "inlineSourceMap": true /* Emit a single file with source maps instead of having a separate file. */,
        "skipLibCheck": true /* Skip type checking of declaration files. */,
        "forceConsistentCasingInFileNames": true /* Disallow inconsistently-cased references to the same file. */,
        "allowUnreachableCode": false,
        "emitDecoratorMetadata": true, // TypeORM
        "experimentalDecorators": true, // TypeORM
        "strictPropertyInitialization": false // for entity to run without constructor
    }
}