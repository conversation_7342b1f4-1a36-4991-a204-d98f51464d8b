import { RouteRegistry } from './types';
import { authHandlers } from './handlers/auth';
import { invoiceHandlers } from './handlers/invoice';
import { mediaHandlers } from './handlers/media';
import { csrfHandlers } from './handlers/csrf';

/**
 * Central route registry containing all API routes
 * This is the single source of truth for all API endpoints
 */
export const routeRegistry: RouteRegistry = {
  // Authentication routes - handled by query parameters in /auth route
  'POST:/auth/login': {
    path: '/auth/login',
    method: 'POST',
    handler: authHandlers.login,
    requiresAuth: false,
    rateLimit: {
      requests: 5,
      windowMs: 60000, // 1 minute
    },
    description: 'User login endpoint',
    tags: ['authentication'],
  },

  'POST:/auth/logout': {
    path: '/auth/logout',
    method: 'POST',
    handler: authHandlers.logout,
    requiresAuth: false,
    rateLimit: {
      requests: 10,
      windowMs: 60000, // 1 minute
    },
    description: 'User logout endpoint',
    tags: ['authentication'],
  },

  'GET:/auth/me': {
    path: '/auth/me',
    method: 'GET',
    handler: authHandlers.me,
    requiresAuth: true,
    rateLimit: {
      requests: 30,
      windowMs: 60000, // 1 minute
    },
    description: 'Get current user info',
    tags: ['authentication'],
  },

  // Invoice routes
  'GET:/invoices': {
    path: '/invoices',
    method: 'GET',
    handler: invoiceHandlers.list,
    requiresAuth: true,
    rateLimit: {
      requests: 30,
      windowMs: 60000, // 1 minute
    },
    description: 'List invoices with pagination',
    tags: ['invoices'],
  },

  'POST:/invoices': {
    path: '/invoices',
    method: 'POST',
    handler: invoiceHandlers.create,
    requiresAuth: true,
    rateLimit: {
      requests: 10,
      windowMs: 60000, // 1 minute
    },
    description: 'Create a new invoice',
    tags: ['invoices'],
  },

  'GET:/invoices/:id': {
    path: '/invoices/:id',
    method: 'GET',
    handler: invoiceHandlers.getById,
    requiresAuth: true,
    rateLimit: {
      requests: 60,
      windowMs: 60000, // 1 minute
    },
    description: 'Get invoice details by ID',
    tags: ['invoices'],
  },

  // Media routes
  'GET:/invoices/download/:filename': {
    path: '/invoices/download/:filename',
    method: 'GET',
    handler: mediaHandlers.downloadInvoice,
    requiresAuth: false,
    rateLimit: {
      requests: 10,
      windowMs: 60000, // 1 minute
    },
    description: 'Download invoice file',
    tags: ['media'],
  },

  // CSRF routes
  'GET:/csrf': {
    path: '/csrf',
    method: 'GET',
    handler: csrfHandlers.getToken,
    requiresAuth: true,
    description: 'Get CSRF token',
    tags: ['security'],
  },
};

/**
 * Get all routes for a specific tag
 */
export function getRoutesByTag(tag: string) {
  return Object.values(routeRegistry).filter(route =>
    route.tags?.includes(tag)
  );
}

/**
 * Get all available routes
 */
export function getAllRoutes() {
  return Object.values(routeRegistry);
}

/**
 * Get route by key (method:path)
 */
export function getRoute(method: string, path: string) {
  const key = `${method.toUpperCase()}:${path}`;
  return routeRegistry[key];
}

/**
 * Register a new route
 */
export function registerRoute(key: string, definition: any) {
  routeRegistry[key] = definition;
}

/**
 * Get all route paths for documentation
 */
export function getRoutePaths() {
  return Object.keys(routeRegistry).map(key => {
    const [method, path] = key.split(':');
    return { method, path };
  });
}
