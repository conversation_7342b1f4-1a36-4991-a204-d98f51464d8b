/**
 * CSRF Protection Configuration
 * Centralized configuration for CSRF protection settings
 */

export interface CSRFConfig {
    // Token settings
    tokenLength: number;
    tokenExpiry: number; // in milliseconds

    // HTTP headers
    headerName: string;
    cookieName: string;

    // Security settings
    secureCookies: boolean;
    sameSite: 'strict' | 'lax' | 'none';
    httpOnly: boolean;

    // Cleanup and maintenance
    cleanupInterval: number; // in milliseconds

    // Validation settings
    skipMethods: string[];
    trustedOrigins: string[];

    // Error handling
    enableDetailedErrors: boolean;
    logFailures: boolean;
}

/**
 * Default CSRF configuration
 */
export const DEFAULT_CSRF_CONFIG: CSRFConfig = {
    // Token settings
    tokenLength: 32, // 32 bytes = 256 bits
    tokenExpiry: 60 * 60 * 1000, // 1 hour

    // HTTP headers
    headerName: 'x-csrf-token',
    cookieName: 'csrf-token',

    // Security settings
    secureCookies: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    httpOnly: true,

    // Cleanup and maintenance
    cleanupInterval: 5 * 60 * 1000, // 5 minutes

    // Validation settings
    skipMethods: ['GET', 'HEAD', 'OPTIONS'],
    trustedOrigins: [
        'http://localhost:3000',
        'http://localhost:3001',
        process.env.FRONTEND_URL || 'http://localhost:3001'
    ].filter(Boolean),

    // Error handling
    enableDetailedErrors: process.env.NODE_ENV !== 'production',
    logFailures: true
};

/**
 * Environment-based CSRF configuration
 */
export const CSRF_CONFIG: CSRFConfig = {
    ...DEFAULT_CSRF_CONFIG,

    // Override with environment variables
    tokenLength: parseInt(process.env.CSRF_TOKEN_LENGTH || '32', 10),
    tokenExpiry: parseInt(process.env.CSRF_TOKEN_EXPIRY || '3600000', 10), // 1 hour default

    headerName: process.env.CSRF_HEADER_NAME || DEFAULT_CSRF_CONFIG.headerName,
    cookieName: process.env.CSRF_COOKIE_NAME || DEFAULT_CSRF_CONFIG.cookieName,

    secureCookies: process.env.CSRF_SECURE_COOKIES === 'true' || process.env.NODE_ENV === 'production',
    sameSite: (process.env.CSRF_SAME_SITE as 'strict' | 'lax' | 'none') || DEFAULT_CSRF_CONFIG.sameSite,
    httpOnly: process.env.CSRF_HTTP_ONLY !== 'false', // default to true unless explicitly disabled

    cleanupInterval: parseInt(process.env.CSRF_CLEANUP_INTERVAL || '300000', 10), // 5 minutes default

    enableDetailedErrors: process.env.CSRF_DETAILED_ERRORS === 'true' || process.env.NODE_ENV !== 'production',
    logFailures: process.env.CSRF_LOG_FAILURES !== 'false' // default to true unless explicitly disabled
};

/**
 * Validate CSRF configuration
 */
export function validateCSRFConfig(config: CSRFConfig): void {
    if (config.tokenLength < 16) {
        throw new Error('CSRF token length must be at least 16 bytes for security');
    }

    if (config.tokenExpiry < 60000) {
        // 1 minute
        throw new Error('CSRF token expiry must be at least 1 minute');
    }

    if (config.tokenExpiry > 24 * 60 * 60 * 1000) {
        // 24 hours
        console.warn('CSRF token expiry is longer than 24 hours, consider reducing for better security');
    }

    if (config.cleanupInterval < 60000) {
        // 1 minute
        throw new Error('CSRF cleanup interval must be at least 1 minute');
    }

    if (!config.headerName || !config.cookieName) {
        throw new Error('CSRF header name and cookie name must be specified');
    }

    if (config.secureCookies && config.sameSite === 'none') {
        console.warn('Using sameSite=none with secure cookies may cause issues in some browsers');
    }
}

/**
 * Get CSRF configuration with validation
 */
export function getCSRFConfig(): CSRFConfig {
    validateCSRFConfig(CSRF_CONFIG);
    return CSRF_CONFIG;
}

/**
 * CSRF configuration for different environments
 */
export const CSRF_CONFIGS = {
    development: {
        ...DEFAULT_CSRF_CONFIG,
        secureCookies: false,
        enableDetailedErrors: true,
        logFailures: true
    },

    production: {
        ...DEFAULT_CSRF_CONFIG,
        secureCookies: true,
        enableDetailedErrors: false,
        logFailures: true,
        tokenExpiry: 30 * 60 * 1000 // 30 minutes in production
    },

    test: {
        ...DEFAULT_CSRF_CONFIG,
        secureCookies: false,
        enableDetailedErrors: true,
        logFailures: false,
        tokenExpiry: 5 * 60 * 1000, // 5 minutes for tests
        cleanupInterval: 30 * 1000 // 30 seconds for tests
    }
} as const;

/**
 * Get environment-specific CSRF configuration
 */
export function getEnvironmentCSRFConfig(env: keyof typeof CSRF_CONFIGS = 'development'): CSRFConfig {
    const config = CSRF_CONFIGS[env] || CSRF_CONFIGS.development;
    validateCSRFConfig(config);
    return config;
}
