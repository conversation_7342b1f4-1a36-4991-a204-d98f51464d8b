# Environment Configuration
NODE_ENV=development
PORT=3000

# Database Configuration (PostgreSQL)
DATABASE_NAME=your_database_name
DATABASE_USER=your_database_user
DATABASE_PASS=your_database_password
DATABASE_HOST=localhost
DATABASE_PORT=5432

# Encryption Configuration
ENCRYPTION_KEY=your_encryption_key_here

# Email Configuration (Optional)
MAIL_TRANSPORT_METHOD=SMTP
MAIL_HOST=smtp.example.com
MAIL_SECURE_CONNECTION=true
MAIL_FROM_NAME=Your App Name
MAIL_USERNAME=your_email_username
MAIL_PASSWORD=your_email_password
MAIL_PORT=465
MAIL_SENDER=<EMAIL>

# Firebase Configuration (Optional)
SERVERKRY=your_firebase_server_key
COLLPASE_KEY=your_collapse_key

# Redis Configuration (Optional)
REDIS_PORT=6379
REDIS_URL=localhost
REDIS_PASS=your_redis_password

# AWS Configuration (Optional)
AWS_URL=https://your-bucket.s3.region.amazonaws.com/
AWS_ID=your_aws_access_key_id
AWS_SECRET=your_aws_secret_access_key
BUCKETNAME=your_bucket_name

# Application URLs
MEDIA_LINK=http://localhost:3000/
SERVER_URL=http://localhost:3000/
USESERVER=true
MODE=development

# Logging Configuration (Optional)
SENTRY_DSN=your_sentry_dsn
DISCORD_DSN=your_discord_webhook_url
