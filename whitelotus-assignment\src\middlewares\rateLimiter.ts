import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { logger } from '../utils/logger';

/**
 * Rate limiter for login attempts
 * Allows 5 login attempts per 15 minutes per IP
 */
export const loginRateLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // Limit each IP to 5 requests per windowMs
    message: {
        status: 429,
        message: 'Too many login attempts from this IP, please try again after 15 minutes.'
    },
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    handler: (req: Request, res: Response) => {
        logger.warn(`Rate limit exceeded for login attempt from IP: ${req.ip}`);
        res.status(429).json({
            status: 429,
            message: 'Too many login attempts from this IP, please try again after 15 minutes.'
        });
    },
    skip: (req: Request) => {
        // Skip rate limiting for certain IPs if needed (e.g., internal services)
        const trustedIPs = ['127.0.0.1', '::1']; // localhost IPs
        return trustedIPs.includes(req.ip || '');
    }
});

/**
 * Rate limiter for sensitive routes (invoice creation, etc.)
 * Allows 20 requests per 15 minutes per IP
 */
export const sensitiveRouteRateLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 20, // Limit each IP to 20 requests per windowMs
    message: {
        status: 429,
        message: 'Too many requests from this IP, please try again after 15 minutes.'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req: Request, res: Response) => {
        logger.warn(`Rate limit exceeded for sensitive route from IP: ${req.ip}, Route: ${req.path}`);
        res.status(429).json({
            status: 429,
            message: 'Too many requests from this IP, please try again after 15 minutes.'
        });
    },
    skip: (req: Request) => {
        const trustedIPs = ['127.0.0.1', '::1'];
        return trustedIPs.includes(req.ip || '');
    }
});

/**
 * General API rate limiter
 * Allows 100 requests per 15 minutes per IP
 */
export const generalRateLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: {
        status: 429,
        message: 'Too many requests from this IP, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req: Request, res: Response) => {
        logger.warn(`General rate limit exceeded from IP: ${req.ip}, Route: ${req.path}`);
        res.status(429).json({
            status: 429,
            message: 'Too many requests from this IP, please try again later.'
        });
    }
});
