"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[579],{125:(e,t,r)=>{var n=r(2115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,u=n.useLayoutEffect,l=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,s=n[1];return u(function(){i.value=r,i.getSnapshot=t,c(i)&&s({inst:i})},[e,r,t]),o(function(){return c(i)&&s({inst:i}),e(function(){c(i)&&s({inst:i})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:s},1026:(e,t,r)=>{let n;r.d(t,{Ay:()=>Q});var i=r(2115),a=r(4806),o=Object.prototype.hasOwnProperty;let u=new WeakMap,l=()=>{},c=l(),s=Object,d=e=>e===c,f=(e,t)=>({...e,...t}),h={},p={},y="undefined",g=typeof window!=y,w=typeof document!=y,v=g&&"Deno"in window,m=(e,t)=>{let r=u.get(e);return[()=>!d(t)&&e.get(t)||h,n=>{if(!d(t)){let i=e.get(t);t in p||(p[t]=i),r[5](t,f(i,n),i||h)}},r[6],()=>!d(t)&&t in p?p[t]:!d(t)&&e.get(t)||h]},b=!0,[k,E]=g&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[l,l],O={initFocus:e=>(w&&document.addEventListener("visibilitychange",e),k("focus",e),()=>{w&&document.removeEventListener("visibilitychange",e),E("focus",e)}),initReconnect:e=>{let t=()=>{b=!0,e()},r=()=>{b=!1};return k("online",t),k("offline",r),()=>{E("online",t),E("offline",r)}}},S=!i.useId,_=!g||v,R=_?i.useEffect:i.useLayoutEffect,A="undefined"!=typeof navigator&&navigator.connection,L=!_&&A&&(["slow-2g","2g"].includes(A.effectiveType)||A.saveData),x=new WeakMap,C=(e,t)=>e==="[object ".concat(t,"]"),T=0,V=e=>{let t,r,n=typeof e,i=s.prototype.toString.call(e),a=C(i,"Date"),o=C(i,"RegExp"),u=C(i,"Object");if(s(e)!==e||a||o)t=a?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=x.get(e))return t;if(t=++T+"~",x.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=V(e[r])+",";x.set(e,t)}if(u){t="#";let n=s.keys(e).sort();for(;!d(r=n.pop());)d(e[r])||(t+=r+":"+V(e[r])+",");x.set(e,t)}}return t},j=e=>{if("function"==typeof e)try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?V(e):"",t]},M=0,D=()=>++M;async function P(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,i,a,o]=t,l=f({populateCache:!0,throwOnError:!0},"boolean"==typeof o?{revalidate:o}:o||{}),s=l.populateCache,h=l.rollbackOnError,p=l.optimisticData,y=l.throwOnError;if("function"==typeof i){let e=[];for(let t of n.keys())!/^\$(inf|sub)\$/.test(t)&&i(n.get(t)._k)&&e.push(t);return Promise.all(e.map(g))}return g(i);async function g(e){let r,[i]=j(e);if(!i)return;let[o,f]=m(n,i),[g,w,v,b]=u.get(n),k=()=>{let t=g[i];return("function"==typeof l.revalidate?l.revalidate(o().data,e):!1!==l.revalidate)&&(delete v[i],delete b[i],t&&t[0])?t[0](2).then(()=>o().data):o().data};if(t.length<3)return k();let E=a,O=!1,S=D();w[i]=[S,0];let _=!d(p),R=o(),A=R.data,L=R._c,x=d(L)?A:L;if(_&&f({data:p="function"==typeof p?p(x,A):p,_c:x}),"function"==typeof E)try{E=E(x)}catch(e){r=e,O=!0}if(E&&"function"==typeof E.then){let e;if(E=await E.catch(e=>{r=e,O=!0}),S!==w[i][0]){if(O)throw r;return E}O&&_&&(e=r,"function"==typeof h?h(e):!1!==h)&&(s=!0,f({data:x,_c:c}))}if(s&&!O&&("function"==typeof s?f({data:s(E,x),error:c,_c:c}):f({data:E,error:c,_c:c})),w[i][1]=D(),Promise.resolve(k()).then(()=>{f({_c:c})}),O){if(y)throw r;return}return E}}let W=(e,t)=>{for(let r in e)e[r][0]&&e[r][0](t)},F=(e,t)=>{if(!u.has(e)){let r=f(O,t),n=Object.create(null),i=P.bind(c,e),a=l,o=Object.create(null),s=(e,t)=>{let r=o[e]||[];return o[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},d=(t,r,n)=>{e.set(t,r);let i=o[t];if(i)for(let e of i)e(r,n)},h=()=>{if(!u.has(e)&&(u.set(e,[n,Object.create(null),Object.create(null),Object.create(null),i,d,s]),!_)){let t=r.initFocus(setTimeout.bind(c,W.bind(c,n,0))),i=r.initReconnect(setTimeout.bind(c,W.bind(c,n,1)));a=()=>{t&&t(),i&&i(),u.delete(e)}}};return h(),[e,i,h,a]}return[e,u.get(e)[4]]},[I,N]=F(new Map),$=f({onLoadingSlow:l,onSuccess:l,onError:l,onErrorRetry:(e,t,r,n,i)=>{let a=r.errorRetryCount,o=i.retryCount,u=~~((Math.random()+.5)*(1<<(o<8?o:8)))*r.errorRetryInterval;(d(a)||!(o>a))&&setTimeout(n,u,i)},onDiscarded:l,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:L?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:L?5e3:3e3,compare:function e(t,r){var n,i;if(t===r)return!0;if(t&&r&&(n=t.constructor)===r.constructor){if(n===Date)return t.getTime()===r.getTime();if(n===RegExp)return t.toString()===r.toString();if(n===Array){if((i=t.length)===r.length)for(;i--&&e(t[i],r[i]););return -1===i}if(!n||"object"==typeof t){for(n in i=0,t)if(o.call(t,n)&&++i&&!o.call(r,n)||!(n in r)||!e(t[n],r[n]))return!1;return Object.keys(r).length===i}}return t!=t&&r!=r},isPaused:()=>!1,cache:I,mutate:N,fallback:{}},{isOnline:()=>b,isVisible:()=>{let e=w&&document.visibilityState;return d(e)||"hidden"!==e}}),q=(e,t)=>{let r=f(e,t);if(t){let{use:n,fallback:i}=e,{use:a,fallback:o}=t;n&&a&&(r.use=n.concat(a)),i&&o&&(r.fallback=f(i,o))}return r},U=(0,i.createContext)({}),H=g&&window.__SWR_DEVTOOLS_USE__,z=(H?window.__SWR_DEVTOOLS_USE__:[]).concat(e=>(t,r,n)=>{let i=r&&((...e)=>{let[n]=j(t),[,,,i]=u.get(I);if(n.startsWith("$inf$"))return r(...e);let a=i[n];return d(a)?r(...e):(delete i[n],a)});return e(t,i,n)});H&&(window.__SWR_DEVTOOLS_REACT__=i);let J=()=>{},Z=J();new WeakMap;let B=i.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),G={dedupe:!0},K=Promise.resolve(c);s.defineProperty(e=>{let{value:t}=e,r=(0,i.useContext)(U),n="function"==typeof t,a=(0,i.useMemo)(()=>n?t(r):t,[n,r,t]),o=(0,i.useMemo)(()=>n?a:q(r,a),[n,r,a]),u=a&&a.provider,l=(0,i.useRef)(c);u&&!l.current&&(l.current=F(u(o.cache||I),a));let s=l.current;return s&&(o.cache=s[0],o.mutate=s[1]),R(()=>{if(s)return s[2]&&s[2](),s[3]},[]),(0,i.createElement)(U.Provider,f(e,{value:o}))},"defaultValue",{value:$});let Q=(n=(e,t,r)=>{let{cache:n,compare:o,suspense:l,fallbackData:s,revalidateOnMount:h,revalidateIfStale:p,refreshInterval:w,refreshWhenHidden:v,refreshWhenOffline:b,keepPreviousData:k}=r,[E,O,A,L]=u.get(n),[x,C]=j(e),T=(0,i.useRef)(!1),V=(0,i.useRef)(!1),M=(0,i.useRef)(x),W=(0,i.useRef)(t),F=(0,i.useRef)(r),I=()=>F.current.isVisible()&&F.current.isOnline(),[N,$,q,U]=m(n,x),H=(0,i.useRef)({}).current,z=d(s)?d(r.fallback)?c:r.fallback[x]:s,J=(e,t)=>{for(let r in H)if("data"===r){if(!o(e[r],t[r])&&(!d(e[r])||!o(ei,t[r])))return!1}else if(t[r]!==e[r])return!1;return!0},Z=(0,i.useMemo)(()=>{let e=!!x&&!!t&&(d(h)?!F.current.isPaused()&&!l&&!1!==p:h),r=t=>{let r=f(t);return(delete r._k,e)?{isValidating:!0,isLoading:!0,...r}:r},n=N(),i=U(),a=r(n),o=n===i?a:r(i),u=a;return[()=>{let e=r(N());return J(e,u)?(u.data=e.data,u.isLoading=e.isLoading,u.isValidating=e.isValidating,u.error=e.error,u):(u=e,e)},()=>o]},[n,x]),Q=(0,a.useSyncExternalStore)((0,i.useCallback)(e=>q(x,(t,r)=>{J(r,t)||e()}),[n,x]),Z[0],Z[1]),X=!T.current,Y=E[x]&&E[x].length>0,ee=Q.data,et=d(ee)?z&&"function"==typeof z.then?B(z):z:ee,er=Q.error,en=(0,i.useRef)(et),ei=k?d(ee)?d(en.current)?et:en.current:ee:et,ea=(!Y||!!d(er))&&(X&&!d(h)?h:!F.current.isPaused()&&(l?!d(et)&&p:d(et)||p)),eo=!!(x&&t&&X&&ea),eu=d(Q.isValidating)?eo:Q.isValidating,el=d(Q.isLoading)?eo:Q.isLoading,ec=(0,i.useCallback)(async e=>{let t,n,i=W.current;if(!x||!i||V.current||F.current.isPaused())return!1;let a=!0,u=e||{},l=!A[x]||!u.dedupe,s=()=>S?!V.current&&x===M.current&&T.current:x===M.current,f={isValidating:!1,isLoading:!1},h=()=>{$(f)},p=()=>{let e=A[x];e&&e[1]===n&&delete A[x]},y={isValidating:!0};d(N().data)&&(y.isLoading=!0);try{if(l&&($(y),r.loadingTimeout&&d(N().data)&&setTimeout(()=>{a&&s()&&F.current.onLoadingSlow(x,r)},r.loadingTimeout),A[x]=[i(C),D()]),[t,n]=A[x],t=await t,l&&setTimeout(p,r.dedupingInterval),!A[x]||A[x][1]!==n)return l&&s()&&F.current.onDiscarded(x),!1;f.error=c;let e=O[x];if(!d(e)&&(n<=e[0]||n<=e[1]||0===e[1]))return h(),l&&s()&&F.current.onDiscarded(x),!1;let u=N().data;f.data=o(u,t)?u:t,l&&s()&&F.current.onSuccess(t,x,r)}catch(r){p();let e=F.current,{shouldRetryOnError:t}=e;!e.isPaused()&&(f.error=r,l&&s())&&(e.onError(r,x,e),(!0===t||"function"==typeof t&&t(r))&&(!F.current.revalidateOnFocus||!F.current.revalidateOnReconnect||I())&&e.onErrorRetry(r,x,e,e=>{let t=E[x];t&&t[0]&&t[0](3,e)},{retryCount:(u.retryCount||0)+1,dedupe:!0}))}return a=!1,h(),!0},[x,n]),es=(0,i.useCallback)((...e)=>P(n,M.current,...e),[]);if(R(()=>{W.current=t,F.current=r,d(ee)||(en.current=ee)}),R(()=>{if(!x)return;let e=ec.bind(c,G),t=0;F.current.revalidateOnFocus&&(t=Date.now()+F.current.focusThrottleInterval);let r=((e,t,r)=>{let n=t[e]||(t[e]=[]);return n.push(r),()=>{let e=n.indexOf(r);e>=0&&(n[e]=n[n.length-1],n.pop())}})(x,E,(r,n={})=>{if(0==r){let r=Date.now();F.current.revalidateOnFocus&&r>t&&I()&&(t=r+F.current.focusThrottleInterval,e())}else if(1==r)F.current.revalidateOnReconnect&&I()&&e();else if(2==r)return ec();else if(3==r)return ec(n)});return V.current=!1,M.current=x,T.current=!0,$({_k:C}),ea&&!A[x]&&(d(et)||_?e():(e=>g&&typeof window.requestAnimationFrame!=y?window.requestAnimationFrame(e):setTimeout(e,1))(e)),()=>{V.current=!0,r()}},[x]),R(()=>{let e;function t(){let t="function"==typeof w?w(N().data):w;t&&-1!==e&&(e=setTimeout(r,t))}function r(){!N().error&&(v||F.current.isVisible())&&(b||F.current.isOnline())?ec(G).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[w,v,b,x]),(0,i.useDebugValue)(ei),l){let e=x&&d(et);if(!S&&_&&e)throw Error("Fallback data is required when using Suspense in SSR.");e&&(W.current=t,F.current=r,V.current=!1);let n=L[x];if(B(!d(n)&&e?es(n):K),!d(er)&&e)throw er;let i=e?ec(G):K;!d(ei)&&e&&(i.status="fulfilled",i.value=!0),B(i)}return{mutate:es,get data(){return H.data=!0,ei},get error(){return H.error=!0,er},get isValidating(){return H.isValidating=!0,eu},get isLoading(){return H.isLoading=!0,el}}},function(...e){let t=(()=>{let e=(0,i.useContext)(U);return(0,i.useMemo)(()=>f($,e),[e])})(),[r,a,o]="function"==typeof e[1]?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],u=q(t,o),l=n,{use:c}=u,s=(c||[]).concat(z);for(let e=s.length;e--;)l=s[e](l);return l(r,a||u.fetcher||null,u)})},1761:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(1847).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},1847:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(2115);let i=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:i=24,strokeWidth:u=2,absoluteStrokeWidth:l,className:c="",children:s,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:t,...o,width:i,height:i,stroke:r,strokeWidth:l?24*Number(u)/Number(i):u,className:a("lucide",c),...!s&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(f)&&{"aria-hidden":"true"},...f},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(s)?s:[s]])}),l=(e,t)=>{let r=(0,n.forwardRef)((r,o)=>{let{className:l,...c}=r;return(0,n.createElement)(u,{ref:o,iconNode:t,className:a("lucide-".concat(i(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),l),...c})});return r.displayName=i(e),r}},4806:(e,t,r)=>{e.exports=r(125)},6485:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(1847).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},7828:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(1847).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9867:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(1847).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])}}]);