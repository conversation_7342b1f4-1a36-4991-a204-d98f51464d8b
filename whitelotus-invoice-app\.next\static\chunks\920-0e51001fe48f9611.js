"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[920],{960:(e,t,r)=>{r.d(t,{hS:()=>i,sM:()=>c,uE:()=>o});var s=r(6830),n=r(6895);class a{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat(this.baseURL).concat(e),s={"Content-Type":"application/json",...t.headers};if(t.method&&!["GET","HEAD","OPTIONS"].includes(t.method.toUpperCase()))try{let e=await n.Hi.getToken();s["x-csrf-token"]=e}catch(e){console.warn("Failed to get CSRF token:",e)}let a={...t,headers:s,credentials:"include"};try{let e=new AbortController,t=setTimeout(()=>e.abort(),this.timeout),s=await fetch(r,{...a,signal:e.signal});if(clearTimeout(t),!s.ok){var o;let e=await s.json().catch(()=>({}));throw 403===s.status&&(null==(o=e.message)?void 0:o.includes("CSRF"))&&(console.warn("CSRF token error, clearing token cache"),n.Hi.clearToken()),Error(e.message||"HTTP ".concat(s.status,": ").concat(s.statusText))}return await s.json()}catch(e){if(e instanceof Error){if("AbortError"===e.name)throw Error("Request timeout");throw e}throw Error("An unexpected error occurred")}}async get(e,t){return this.request(e,{method:"GET",headers:t})}async post(e,t,r){return this.request(e,{method:"POST",body:t?JSON.stringify(t):void 0,headers:r})}async put(e,t,r){return this.request(e,{method:"PUT",body:t?JSON.stringify(t):void 0,headers:r})}async delete(e,t){return this.request(e,{method:"DELETE",headers:t})}constructor(){this.baseURL=s.i3.BASE_URL,this.timeout=s.i3.TIMEOUT}}let o=new a;function i(e){return e instanceof Error?{message:e.message,code:"API_ERROR"}:{message:"An unexpected error occurred",code:"UNKNOWN_ERROR"}}function c(e){if(!e||"object"!=typeof e)throw Error("Invalid API response format");if("number"!=typeof e.status)throw Error("Invalid API response: missing status");if("string"!=typeof e.message)throw Error("Invalid API response: missing message");return e}},1920:(e,t,r)=>{r.d(t,{AuthProvider:()=>h,A:()=>E});var s=r(5155),n=r(2115),a=r(63),o=r(960),i=r(6830);class c{async login(e){try{let t=await o.uE.post(i.mM.LOGIN,e);return(0,o.sM)(t)}catch(e){throw(0,o.hS)(e)}}logout(){}validateCredentials(e,t){let r=[];return e?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)||r.push("Please enter a valid email address"):r.push("Email is required"),t?t.length<8&&r.push("Password must be at least 8 characters long"):r.push("Password is required"),r}isTokenExpired(e){try{let t=JSON.parse(atob(e.split(".")[1])),r=Date.now()/1e3;return t.exp<r}catch(e){return!0}}}let l=new c,u=(0,n.createContext)(void 0);function h(e){let{children:t}=e,[r,o]=(0,n.useState)(null),[c,h]=(0,n.useState)(!0),E=(0,a.useRouter)();(0,n.useEffect)(()=>{h(!1)},[]);let d=async(e,t)=>{h(!0);try{let r=await l.login({email:e,password:t});if(200===r.status&&r.data)o(r.data),E.push(i.bw.DASHBOARD);else throw Error(r.message||"Login failed")}catch(e){throw console.error("Login failed:",e),e}finally{h(!1)}};return(0,s.jsx)(u.Provider,{value:{user:r,login:d,logout:()=>{h(!0);try{l.logout(),o(null),E.push(i.bw.LOGIN)}finally{h(!1)}},loading:c,isAuthenticated:!!r},children:t})}function E(){let e=(0,n.useContext)(u);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},6830:(e,t,r)=>{r.d(t,{R7:()=>i,W2:()=>c,bw:()=>a,i3:()=>n,mM:()=>o});var s=r(5704);let n={BASE_URL:"http://localhost:3001/api",TIMEOUT:parseInt(s.env.API_TIMEOUT||"10000",10),RETRIES:parseInt(s.env.API_RETRIES||"3",10)};s.env.JWT_SECRET||console.warn("⚠️  WARNING: Using default JWT_SECRET. Set JWT_SECRET environment variable in production!"),s.env.COOKIE_NAME,parseInt(s.env.COOKIE_MAX_AGE||"604800",10),parseInt(s.env.PAGINATION_DEFAULT_LIMIT||"5",10),parseInt(s.env.PAGINATION_MAX_LIMIT||"100",10),parseInt(s.env.ISR_REVALIDATE||"60",10),parseInt(s.env.RATE_LIMIT_LOGIN_ATTEMPTS||"5",10),parseInt(s.env.RATE_LIMIT_LOGIN_WINDOW||"60000",10),parseInt(s.env.RATE_LIMIT_DOWNLOAD_ATTEMPTS||"20",10),parseInt(s.env.RATE_LIMIT_DOWNLOAD_WINDOW||"60000",10);let a={HOME:"/",LOGIN:"/login",DASHBOARD:"/dashboard",INVOICE_DETAILS:"/invoice",CREATE_INVOICE:"/invoice/new"},o={LOGIN:"/auth/login",LOGOUT:"/auth/logout",ME:"/auth/me"},i={LIST:"/invoices",CREATE:"/invoices",BY_ID:e=>"/invoices/".concat(e)},c={DOWNLOAD_INVOICE:e=>"/invoices/download/".concat(e)};s.env.ADMIN_EMAIL||console.warn("⚠️  WARNING: Using default admin email. Set ADMIN_EMAIL environment variable!"),s.env.ADMIN_PASSWORD||console.warn("⚠️  WARNING: Using default admin password. Set ADMIN_PASSWORD environment variable!"),s.env.DEBUG_LOGGING,parseInt(s.env.PORT||"3000",10)},6895:(e,t,r)=>{r.d(t,{Hi:()=>i});var s=r(2931);class n extends Error{static isCSRFError(e){return e instanceof n}static fromApiResponse(e){let t=e.message.toLowerCase().includes("csrf")||e.message.toLowerCase().includes("token");return 403===e.status&&t?new n(e.message,"CSRF_TOKEN_INVALID",403,!0):new n(e.message||"CSRF validation failed","CSRF_VALIDATION_FAILED",e.status,!0)}getUserMessage(){switch(this.code){case"CSRF_TOKEN_MISSING":return"Security token is missing. Please refresh the page and try again.";case"CSRF_TOKEN_INVALID":return"Security token is invalid. Please refresh the page and try again.";case"CSRF_TOKEN_EXPIRED":return"Security token has expired. Please refresh the page and try again.";case"CSRF_FETCH_FAILED":return"Failed to obtain security token. Please check your connection and try again.";default:return"Security validation failed. Please refresh the page and try again."}}getSuggestedAction(){return this.isRetryable?"Please refresh the page and try again.":"Please contact support if this problem persists."}constructor(e,t="CSRF_ERROR",r=403,s=!0){super(e),this.name="CSRFError",this.code=t,this.statusCode=r,this.isRetryable=s,Error.captureStackTrace&&Error.captureStackTrace(this,n)}}let a={FETCH_FAILED:"CSRF_FETCH_FAILED",VALIDATION_FAILED:"CSRF_VALIDATION_FAILED"};(0,s.A)(window);class o{async getToken(){if(this.token&&Date.now()<this.tokenExpiry)return this.token;if(this.fetchPromise)return this.fetchPromise;this.fetchPromise=this.fetchToken();try{return await this.fetchPromise}finally{this.fetchPromise=null}}async fetchToken(){try{var e;let t=await fetch("/api/csrf-token",{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});if(!t.ok){let e=await t.json().catch(()=>({}));throw new n(e.message||"Failed to fetch CSRF token: ".concat(t.status),a.FETCH_FAILED,t.status)}let r=await t.json();if(200===r.status&&(null==(e=r.data)?void 0:e.csrfToken))return this.token=r.data.csrfToken,this.tokenExpiry=Date.now()+3e6,this.token;throw new n("Invalid CSRF token response",a.VALIDATION_FAILED)}catch(e){if(e instanceof n)throw e;throw console.error("Failed to fetch CSRF token:",e),new n("Network error while fetching CSRF token",a.FETCH_FAILED)}}clearToken(){this.token=null,this.tokenExpiry=0}isTokenExpired(){return!this.token||Date.now()>=this.tokenExpiry}constructor(){this.token=null,this.tokenExpiry=0,this.fetchPromise=null}}let i=new o}}]);