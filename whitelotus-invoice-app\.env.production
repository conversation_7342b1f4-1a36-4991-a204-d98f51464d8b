# Production Environment Configuration for Frontend
NODE_ENV=production

# API Configuration
NEXT_PUBLIC_API_URL=https://whitelotus-backend-api.onrender.com

# Authentication Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
COOKIE_NAME=auth-token
COOKIE_MAX_AGE=604800

# Demo Credentials (Change in production)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Pass@1234

# Pagination Configuration
PAGINATION_DEFAULT_LIMIT=5
PAGINATION_MAX_LIMIT=100

# Security Configuration
CSRF_SECRET=your-csrf-secret-key-change-this-in-production

# Optional: Analytics Configuration
# NEXT_PUBLIC_GA_ID=your-google-analytics-id
# NEXT_PUBLIC_HOTJAR_ID=your-hotjar-id

# Optional: Error Tracking
# NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn

# Optional: Feature Flags
# NEXT_PUBLIC_ENABLE_ANALYTICS=true
# NEXT_PUBLIC_ENABLE_ERROR_TRACKING=true
