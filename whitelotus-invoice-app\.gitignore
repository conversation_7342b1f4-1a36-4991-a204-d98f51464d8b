# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Environment files - exclude all except example files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# Include example environment files (these should be committed)
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
