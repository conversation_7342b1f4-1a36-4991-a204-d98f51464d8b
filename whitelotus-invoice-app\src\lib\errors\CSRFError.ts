/**
 * Custom error class for CSRF-related errors
 */
export class CSRFError extends <PERSON>rror {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly isRetryable: boolean;

  constructor(
    message: string,
    code: string = 'CSRF_ERROR',
    statusCode: number = 403,
    isRetryable: boolean = true
  ) {
    super(message);
    this.name = 'CSRFError';
    this.code = code;
    this.statusCode = statusCode;
    this.isRetryable = isRetryable;

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, CSRFError);
    }
  }

  /**
   * Check if an error is a CSRF error
   */
  static isCSRFError(error: unknown): error is CSRFError {
    return error instanceof CSRFError;
  }

  /**
   * Create CSRF error from API response
   */
  static fromApiResponse(response: { status: number; message: string }): CSRFError {
    const isCSRFRelated = response.message.toLowerCase().includes('csrf') ||
                         response.message.toLowerCase().includes('token');
    
    if (response.status === 403 && isCSRFRelated) {
      return new CSRFError(
        response.message,
        'CSRF_TOKEN_INVALID',
        403,
        true
      );
    }

    return new CSRFError(
      response.message || 'CSRF validation failed',
      'CSRF_VALIDATION_FAILED',
      response.status,
      true
    );
  }

  /**
   * Get user-friendly error message
   */
  getUserMessage(): string {
    switch (this.code) {
      case 'CSRF_TOKEN_MISSING':
        return 'Security token is missing. Please refresh the page and try again.';
      case 'CSRF_TOKEN_INVALID':
        return 'Security token is invalid. Please refresh the page and try again.';
      case 'CSRF_TOKEN_EXPIRED':
        return 'Security token has expired. Please refresh the page and try again.';
      case 'CSRF_FETCH_FAILED':
        return 'Failed to obtain security token. Please check your connection and try again.';
      default:
        return 'Security validation failed. Please refresh the page and try again.';
    }
  }

  /**
   * Get suggested action for the user
   */
  getSuggestedAction(): string {
    if (this.isRetryable) {
      return 'Please refresh the page and try again.';
    }
    return 'Please contact support if this problem persists.';
  }
}

/**
 * CSRF error handler utility
 */
export class CSRFErrorHandler {
  /**
   * Handle CSRF error with appropriate user feedback
   */
  static handleError(error: unknown, onRetry?: () => void): void {
    if (CSRFError.isCSRFError(error)) {
      console.error('CSRF Error:', error);
      
      const userMessage = error.getUserMessage();
      const suggestedAction = error.getSuggestedAction();
      
      // Show user-friendly error message
      if (typeof window !== 'undefined') {
        const shouldRetry = confirm(`${userMessage}\n\n${suggestedAction}\n\nWould you like to refresh the page?`);
        
        if (shouldRetry) {
          if (onRetry) {
            onRetry();
          } else {
            window.location.reload();
          }
        }
      }
    } else {
      // Handle non-CSRF errors
      console.error('Non-CSRF Error:', error);
      throw error;
    }
  }

  /**
   * Create error handler for React components
   */
  static createReactErrorHandler(onRetry?: () => void) {
    return (error: unknown) => {
      this.handleError(error, onRetry);
    };
  }
}

/**
 * CSRF error types
 */
export const CSRF_ERROR_CODES = {
  TOKEN_MISSING: 'CSRF_TOKEN_MISSING',
  TOKEN_INVALID: 'CSRF_TOKEN_INVALID',
  TOKEN_EXPIRED: 'CSRF_TOKEN_EXPIRED',
  FETCH_FAILED: 'CSRF_FETCH_FAILED',
  VALIDATION_FAILED: 'CSRF_VALIDATION_FAILED',
} as const;

export type CSRFErrorCode = typeof CSRF_ERROR_CODES[keyof typeof CSRF_ERROR_CODES];
