# Production Environment Configuration for Backend API
NODE_ENV=production
PORT=10000

# Database Configuration (PostgreSQL on Render)
DATABASE_NAME=whitelotus_invoice_db
DATABASE_USER=whitelotus_invoice_db_user
DATABASE_HOST=dpg-d35e9l6uk2gs73bffqh0-a
DATABASE_PORT=5432
# DATABASE_PASS=<Get from Render dashboard>

# Security Configuration
ENCRYPTION_KEY=your-super-secret-encryption-key-change-this-in-production

# Application URLs (Update with your actual Render URLs)
MEDIA_LINK=https://whitelotus-backend-api.onrender.com/
SERVER_URL=https://whitelotus-backend-api.onrender.com/
USESERVER=true
MODE=production

# Optional: Email Configuration
# MAIL_TRANSPORT_METHOD=SMTP
# MAIL_HOST=smtp.gmail.com
# MAIL_SECURE_CONNECTION=true
# MAIL_FROM_NAME=WhiteLotus Invoice
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password
# MAIL_PORT=587
# MAIL_SENDER=<EMAIL>

# Optional: Redis Configuration (if using Redis)
# REDIS_PORT=6379
# REDIS_URL=your-redis-url
# REDIS_PASS=your-redis-password

# Optional: AWS Configuration (if using S3)
# AWS_URL=https://your-bucket.s3.region.amazonaws.com/
# AWS_ID=your-aws-access-key-id
# AWS_SECRET=your-aws-secret-access-key
# BUCKETNAME=your-bucket-name

# Optional: Logging Configuration
# SENTRY_DSN=your-sentry-dsn
# DISCORD_DSN=your-discord-webhook-url
