import { api<PERSON><PERSON>, handleApiError, validateApiResponse } from '../api';
import { AUTH_API } from '../config';
import { LoginRequest, LoginResponse, User } from '@/types';

export class AuthService {

  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await apiClient.post<LoginResponse>(
        AUTH_API.LOGIN,
        credentials
      );

      return validateApiResponse<User>(response);
    } catch (error) {
      throw handleApiError(error);
    }
  }

  logout(): void {
    // Simply clear client-side state - no API call needed
    // The session will be handled by removing user state from AuthContext
  }



  validateCredentials(email: string, password: string): string[] {
    const errors: string[] = [];

    if (!email) {
      errors.push('Email is required');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.push('Please enter a valid email address');
    }

    if (!password) {
      errors.push('Password is required');
    } else if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    return errors;
  }

  isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch (error) {
      return true;
    }
  }
}

export const authService = new AuthService();
