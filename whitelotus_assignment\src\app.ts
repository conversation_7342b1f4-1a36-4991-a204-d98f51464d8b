import express, { RequestHand<PERSON> } from 'express';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import json2xls from 'json2xls';
import helmet from 'helmet';
import apiV1UnAuthRoutes from './routes/unAuthRoute';
import apiV1AuthRoutes from './routes/authRoute';
import csrfRoutes from './routes/csrfRoute';
import { logger, accessSuccessLogger, accessErrorLogger } from './utils/logger';
import { tokenHandler, generalRateLimiter } from './middlewares';
import { generateCSRFMiddleware } from './middlewares/csrfMiddleware';
import path from 'path';
import { httpStatusCodes } from './utils/constants';
import uploadHandler from './utils/helpers/upload';
import multer from 'multer';
import mediaRoutes from './routes/mediaRoutes';
import { setupSwagger } from './config/swagger';

const app = express();

const upload = multer();

// Security middleware - Helmet for security headers
app.use(
    helmet({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
                fontSrc: ["'self'", 'https://fonts.gstatic.com'],
                imgSrc: ["'self'", 'data:', 'https:'],
                scriptSrc: ["'self'"],
                connectSrc: ["'self'"],
                frameSrc: ["'none'"],
                objectSrc: ["'none'"],
                mediaSrc: ["'self'"],
                manifestSrc: ["'self'"]
            }
        },
        crossOriginEmbedderPolicy: false, // Disable for API compatibility
        hsts: {
            maxAge: 31536000, // 1 year
            includeSubDomains: true,
            preload: true
        }
    })
);

// Apply general rate limiting to all requests
app.use(generalRateLimiter);

// register loggers
app.use(accessSuccessLogger);
app.use(accessErrorLogger);
app.use(express.static(path.join(__dirname, 'public')));

app.disable('x-powered-by');
app.use(express.json() as RequestHandler);
app.use(express.urlencoded({ extended: true }) as RequestHandler);
app.use(json2xls.middleware);

app.use(
    cors({
        credentials: true,
        origin: ['http://localhost:3000'],
        methods: ['GET', 'POST', 'PUT', 'DELETE']
    })
);
app.use(cookieParser('CookieSecret'));

app.all('/*', (req, res, next) => {
    // 	// CORS headers
    res.header('Access-Control-Allow-Origin', '*'); // restrict it to the required domain
    res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
    // Set custom headers for CORS
    res.header('Access-Control-Allow-Headers', 'Content-type,Accept,X-Access-Token,X-Key,Authorization,Client-Key');

    if (req.method === 'OPTIONS') {
        res.status(httpStatusCodes.SUCCESS_CODE).end();
    } else {
        next();
    }
});

app.use('/file/upload', uploadHandler);

// parse FormData
app.use(upload.any());

app.use('/api', apiV1UnAuthRoutes);
app.use('/api', csrfRoutes); // CSRF routes (available to all)
app.use('/api/auth', tokenHandler, generateCSRFMiddleware, apiV1AuthRoutes);

app.use('/', mediaRoutes);

// Setup Swagger documentation
setupSwagger(app);

// setTimeout(() => {
//     policyViolations.checkViolationThreshold();
// }, 1000)

// Health check endpoint for Docker
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// global error handler
// TODO: save error in one file and sending mail on error
app.use((err, req, res, next) => {
    logger.error(`!!!!!!Global Error!!!!!!! ${err.stack}`);

    res.status(500).json({
        status: 500,
        message:
            typeof err === 'string' ? err : typeof err?.message === 'string' ? err?.message : 'Internal Server Error',
        error: err
    });
});

export = app;
