import { NextFunction, Request, Response, Router } from 'express';
import invoice from '../controllers/invoice';
import { fieldsValidator } from '../middlewares/validator';
import { sensitiveRouteRateLimiter } from '../middlewares/rateLimiter';
import { validateCSRFMiddleware } from '../middlewares/csrfMiddleware';
import { createInvoiceSchema } from '../utils/schema/invoice_schema';

const router = Router();

/**
 * Total Auth Routes
 */

// list invoices
router.get('/invoice', invoice.listInvoice);

// get invoice details
router.get('/invoice/:id', invoice.invoiceDetails);

// create invoice
router.post(
    '/invoice',
    sensitiveRouteRateLimiter, // Apply rate limiting to invoice creation
    validateCSRFMiddleware, // CSRF protection for state-changing operations
    (req: Request, res: Response, next: NextFunction) => fieldsValidator(req, res, next, createInvoiceSchema),
    invoice.createInvoice
);

export default router;
