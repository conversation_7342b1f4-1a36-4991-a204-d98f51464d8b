(()=>{var a={};a.id=105,a.ids=[105],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1236:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(7954).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\Workspace\\\\Whitelotus assignment\\\\whitelotus-invoice-app\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Workspace\\Whitelotus assignment\\whitelotus-invoice-app\\src\\app\\dashboard\\page.tsx","default")},2797:(a,b,c)=>{"use strict";var d=c(8301),e="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},f=d.useState,g=d.useEffect,h=d.useLayoutEffect,i=d.useDebugValue;function j(a){var b=a.getSnapshot;a=a.value;try{var c=b();return!e(a,c)}catch(a){return!0}}var k="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(a,b){return b()}:function(a,b){var c=b(),d=f({inst:{value:c,getSnapshot:b}}),e=d[0].inst,k=d[1];return h(function(){e.value=c,e.getSnapshot=b,j(e)&&k({inst:e})},[a,c,b]),g(function(){return j(e)&&k({inst:e}),a(function(){j(e)&&k({inst:e})})},[a]),i(c),c};b.useSyncExternalStore=void 0!==d.useSyncExternalStore?d.useSyncExternalStore:k},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3042:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(9754),e=c(9117),f=c(6595),g=c(2324),h=c(9326),i=c(8928),j=c(175),k=c(12),l=c(4290),m=c(2696),n=c(2802),o=c(7533),p=c(5229),q=c(2822),r=c(261),s=c(6453),t=c(2474),u=c(6713),v=c(1356),w=c(2685),x=c(6225),y=c(3446),z=c(2762),A=c(5742),B=c(6439),C=c(1170),D=c.n(C),E=c(2506),F=c(1203),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1236)),"E:\\Workspace\\Whitelotus assignment\\whitelotus-invoice-app\\src\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,1472)),"E:\\Workspace\\Whitelotus assignment\\whitelotus-invoice-app\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,1170,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,7028,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,461,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,2768,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,I=["E:\\Workspace\\Whitelotus assignment\\whitelotus-invoice-app\\src\\app\\dashboard\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/dashboard/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(9902).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3814:(a,b,c)=>{"use strict";let d;c.r(b),c.d(b,{default:()=>ao});var e=c(1124),f=c(2278),g=c(8301),h=c(9088),i=Object.prototype.hasOwnProperty;let j=new WeakMap,k=()=>{},l=k(),m=Object,n=a=>a===l,o=(a,b)=>({...a,...b}),p={},q={},r="undefined",s=typeof document!=r,t=!1,u=(a,b)=>{let c=j.get(a);return[()=>!n(b)&&a.get(b)||p,d=>{if(!n(b)){let e=a.get(b);b in q||(q[b]=e),c[5](b,o(e,d),e||p)}},c[6],()=>!n(b)&&b in q?q[b]:!n(b)&&a.get(b)||p]},v=!0,[w,x]=[k,k],y={initFocus:a=>(s&&document.addEventListener("visibilitychange",a),w("focus",a),()=>{s&&document.removeEventListener("visibilitychange",a),x("focus",a)}),initReconnect:a=>{let b=()=>{v=!0,a()},c=()=>{v=!1};return w("online",b),w("offline",c),()=>{x("online",b),x("offline",c)}}},z=!g.useId,A=!0,B=A?g.useEffect:g.useLayoutEffect,C="undefined"!=typeof navigator&&navigator.connection,D=!A&&C&&(["slow-2g","2g"].includes(C.effectiveType)||C.saveData),E=new WeakMap,F=(a,b)=>a===`[object ${b}]`,G=0,H=a=>{let b,c,d=typeof a,e=m.prototype.toString.call(a),f=F(e,"Date"),g=F(e,"RegExp"),h=F(e,"Object");if(m(a)!==a||f||g)b=f?a.toJSON():"symbol"==d?a.toString():"string"==d?JSON.stringify(a):""+a;else{if(b=E.get(a))return b;if(b=++G+"~",E.set(a,b),Array.isArray(a)){for(c=0,b="@";c<a.length;c++)b+=H(a[c])+",";E.set(a,b)}if(h){b="#";let d=m.keys(a).sort();for(;!n(c=d.pop());)n(a[c])||(b+=c+":"+H(a[c])+",");E.set(a,b)}}return b},I=a=>{if("function"==typeof a)try{a=a()}catch(b){a=""}let b=a;return[a="string"==typeof a?a:(Array.isArray(a)?a.length:a)?H(a):"",b]},J=0,K=()=>++J;async function L(...a){let[b,c,d,e]=a,f=o({populateCache:!0,throwOnError:!0},"boolean"==typeof e?{revalidate:e}:e||{}),g=f.populateCache,h=f.rollbackOnError,i=f.optimisticData,k=f.throwOnError;if("function"==typeof c){let a=[];for(let d of b.keys())!/^\$(inf|sub)\$/.test(d)&&c(b.get(d)._k)&&a.push(d);return Promise.all(a.map(m))}return m(c);async function m(c){let e,[m]=I(c);if(!m)return;let[o,p]=u(b,m),[q,r,s,t]=j.get(b),v=()=>{let a=q[m];return("function"==typeof f.revalidate?f.revalidate(o().data,c):!1!==f.revalidate)&&(delete s[m],delete t[m],a&&a[0])?a[0](2).then(()=>o().data):o().data};if(a.length<3)return v();let w=d,x=!1,y=K();r[m]=[y,0];let z=!n(i),A=o(),B=A.data,C=A._c,D=n(C)?B:C;if(z&&p({data:i="function"==typeof i?i(D,B):i,_c:D}),"function"==typeof w)try{w=w(D)}catch(a){e=a,x=!0}if(w&&"function"==typeof w.then){let a;if(w=await w.catch(a=>{e=a,x=!0}),y!==r[m][0]){if(x)throw e;return w}x&&z&&(a=e,"function"==typeof h?h(a):!1!==h)&&(g=!0,p({data:D,_c:l}))}if(g&&!x&&("function"==typeof g?p({data:g(w,D),error:l,_c:l}):p({data:w,error:l,_c:l})),r[m][1]=K(),Promise.resolve(v()).then(()=>{p({_c:l})}),x){if(k)throw e;return}return w}}let M=(a,b)=>{for(let c in a)a[c][0]&&a[c][0](b)},N=(a,b)=>{if(!j.has(a)){let c=o(y,b),d=Object.create(null),e=L.bind(l,a),f=k,g=Object.create(null),h=(a,b)=>{let c=g[a]||[];return g[a]=c,c.push(b),()=>c.splice(c.indexOf(b),1)},i=(b,c,d)=>{a.set(b,c);let e=g[b];if(e)for(let a of e)a(c,d)},m=()=>{if(!j.has(a)&&(j.set(a,[d,Object.create(null),Object.create(null),Object.create(null),e,i,h]),!A)){let b=c.initFocus(setTimeout.bind(l,M.bind(l,d,0))),e=c.initReconnect(setTimeout.bind(l,M.bind(l,d,1)));f=()=>{b&&b(),e&&e(),j.delete(a)}}};return m(),[a,e,m,f]}return[a,j.get(a)[4]]},[O,P]=N(new Map),Q=o({onLoadingSlow:k,onSuccess:k,onError:k,onErrorRetry:(a,b,c,d,e)=>{let f=c.errorRetryCount,g=e.retryCount,h=~~((Math.random()+.5)*(1<<(g<8?g:8)))*c.errorRetryInterval;(n(f)||!(g>f))&&setTimeout(d,h,e)},onDiscarded:k,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:D?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:D?5e3:3e3,compare:function a(b,c){var d,e;if(b===c)return!0;if(b&&c&&(d=b.constructor)===c.constructor){if(d===Date)return b.getTime()===c.getTime();if(d===RegExp)return b.toString()===c.toString();if(d===Array){if((e=b.length)===c.length)for(;e--&&a(b[e],c[e]););return -1===e}if(!d||"object"==typeof b){for(d in e=0,b)if(i.call(b,d)&&++e&&!i.call(c,d)||!(d in c)||!a(b[d],c[d]))return!1;return Object.keys(c).length===e}}return b!=b&&c!=c},isPaused:()=>!1,cache:O,mutate:P,fallback:{}},{isOnline:()=>v,isVisible:()=>{let a=s&&document.visibilityState;return n(a)||"hidden"!==a}}),R=(a,b)=>{let c=o(a,b);if(b){let{use:d,fallback:e}=a,{use:f,fallback:g}=b;d&&f&&(c.use=d.concat(f)),e&&g&&(c.fallback=o(e,g))}return c},S=(0,g.createContext)({}),T=!1,U=(T?window.__SWR_DEVTOOLS_USE__:[]).concat(a=>(b,c,d)=>{let e=c&&((...a)=>{let[d]=I(b),[,,,e]=j.get(O);if(d.startsWith("$inf$"))return c(...a);let f=e[d];return n(f)?c(...a):(delete e[d],f)});return a(b,e,d)});T&&(window.__SWR_DEVTOOLS_REACT__=g);let V=()=>{},W=V();new WeakMap;let X=g.use||(a=>{switch(a.status){case"pending":throw a;case"fulfilled":return a.value;case"rejected":throw a.reason;default:throw a.status="pending",a.then(b=>{a.status="fulfilled",a.value=b},b=>{a.status="rejected",a.reason=b}),a}}),Y={dedupe:!0},Z=Promise.resolve(l);m.defineProperty(a=>{let{value:b}=a,c=(0,g.useContext)(S),d="function"==typeof b,e=(0,g.useMemo)(()=>d?b(c):b,[d,c,b]),f=(0,g.useMemo)(()=>d?e:R(c,e),[d,c,e]),h=e&&e.provider,i=(0,g.useRef)(l);h&&!i.current&&(i.current=N(h(f.cache||O),e));let j=i.current;return j&&(f.cache=j[0],f.mutate=j[1]),B(()=>{if(j)return j[2]&&j[2](),j[3]},[]),(0,g.createElement)(S.Provider,o(a,{value:f}))},"defaultValue",{value:Q});let $=(d=(a,b,c)=>{let{cache:d,compare:e,suspense:f,fallbackData:i,revalidateOnMount:k,revalidateIfStale:m,refreshInterval:p,refreshWhenHidden:q,refreshWhenOffline:r,keepPreviousData:s}=c,[t,v,w,x]=j.get(d),[y,C]=I(a),D=(0,g.useRef)(!1),E=(0,g.useRef)(!1),F=(0,g.useRef)(y),G=(0,g.useRef)(b),H=(0,g.useRef)(c),J=()=>H.current.isVisible()&&H.current.isOnline(),[M,N,O,P]=u(d,y),Q=(0,g.useRef)({}).current,R=n(i)?n(c.fallback)?l:c.fallback[y]:i,S=(a,b)=>{for(let c in Q)if("data"===c){if(!e(a[c],b[c])&&(!n(a[c])||!e(ac,b[c])))return!1}else if(b[c]!==a[c])return!1;return!0},T=(0,g.useMemo)(()=>{let a=!!y&&!!b&&(n(k)?!H.current.isPaused()&&!f&&!1!==m:k),c=b=>{let c=o(b);return(delete c._k,a)?{isValidating:!0,isLoading:!0,...c}:c},d=M(),e=P(),g=c(d),h=d===e?g:c(e),i=g;return[()=>{let a=c(M());return S(a,i)?(i.data=a.data,i.isLoading=a.isLoading,i.isValidating=a.isValidating,i.error=a.error,i):(i=a,a)},()=>h]},[d,y]),U=(0,h.useSyncExternalStore)((0,g.useCallback)(a=>O(y,(b,c)=>{S(c,b)||a()}),[d,y]),T[0],T[1]),V=!D.current,W=t[y]&&t[y].length>0,$=U.data,_=n($)?R&&"function"==typeof R.then?X(R):R:$,aa=U.error,ab=(0,g.useRef)(_),ac=s?n($)?n(ab.current)?_:ab.current:$:_,ad=(!W||!!n(aa))&&(V&&!n(k)?k:!H.current.isPaused()&&(f?!n(_)&&m:n(_)||m)),ae=!!(y&&b&&V&&ad),af=n(U.isValidating)?ae:U.isValidating,ag=n(U.isLoading)?ae:U.isLoading,ah=(0,g.useCallback)(async a=>{let b,d,f=G.current;if(!y||!f||E.current||H.current.isPaused())return!1;let g=!0,h=a||{},i=!w[y]||!h.dedupe,j=()=>z?!E.current&&y===F.current&&D.current:y===F.current,k={isValidating:!1,isLoading:!1},m=()=>{N(k)},o=()=>{let a=w[y];a&&a[1]===d&&delete w[y]},p={isValidating:!0};n(M().data)&&(p.isLoading=!0);try{if(i&&(N(p),c.loadingTimeout&&n(M().data)&&setTimeout(()=>{g&&j()&&H.current.onLoadingSlow(y,c)},c.loadingTimeout),w[y]=[f(C),K()]),[b,d]=w[y],b=await b,i&&setTimeout(o,c.dedupingInterval),!w[y]||w[y][1]!==d)return i&&j()&&H.current.onDiscarded(y),!1;k.error=l;let a=v[y];if(!n(a)&&(d<=a[0]||d<=a[1]||0===a[1]))return m(),i&&j()&&H.current.onDiscarded(y),!1;let h=M().data;k.data=e(h,b)?h:b,i&&j()&&H.current.onSuccess(b,y,c)}catch(c){o();let a=H.current,{shouldRetryOnError:b}=a;!a.isPaused()&&(k.error=c,i&&j())&&(a.onError(c,y,a),(!0===b||"function"==typeof b&&b(c))&&(!H.current.revalidateOnFocus||!H.current.revalidateOnReconnect||J())&&a.onErrorRetry(c,y,a,a=>{let b=t[y];b&&b[0]&&b[0](3,a)},{retryCount:(h.retryCount||0)+1,dedupe:!0}))}return g=!1,m(),!0},[y,d]),ai=(0,g.useCallback)((...a)=>L(d,F.current,...a),[]);if(B(()=>{G.current=b,H.current=c,n($)||(ab.current=$)}),B(()=>{if(!y)return;let a=ah.bind(l,Y),b=0;H.current.revalidateOnFocus&&(b=Date.now()+H.current.focusThrottleInterval);let c=((a,b,c)=>{let d=b[a]||(b[a]=[]);return d.push(c),()=>{let a=d.indexOf(c);a>=0&&(d[a]=d[d.length-1],d.pop())}})(y,t,(c,d={})=>{if(0==c){let c=Date.now();H.current.revalidateOnFocus&&c>b&&J()&&(b=c+H.current.focusThrottleInterval,a())}else if(1==c)H.current.revalidateOnReconnect&&J()&&a();else if(2==c)return ah();else if(3==c)return ah(d)});return E.current=!1,F.current=y,D.current=!0,N({_k:C}),ad&&!w[y]&&(n(_)||A?a():(a=>setTimeout(a,1))(a)),()=>{E.current=!0,c()}},[y]),B(()=>{let a;function b(){let b="function"==typeof p?p(M().data):p;b&&-1!==a&&(a=setTimeout(c,b))}function c(){!M().error&&(q||H.current.isVisible())&&(r||H.current.isOnline())?ah(Y).then(b):b()}return b(),()=>{a&&(clearTimeout(a),a=-1)}},[p,q,r,y]),(0,g.useDebugValue)(ac),f){let a=y&&n(_);if(!z&&A&&a)throw Error("Fallback data is required when using Suspense in SSR.");a&&(G.current=b,H.current=c,E.current=!1);let d=x[y];if(X(!n(d)&&a?ai(d):Z),!n(aa)&&a)throw aa;let e=a?ah(Y):Z;!n(ac)&&a&&(e.status="fulfilled",e.value=!0),X(e)}return{mutate:ai,get data(){return Q.data=!0,ac},get error(){return Q.error=!0,aa},get isValidating(){return Q.isValidating=!0,af},get isLoading(){return Q.isLoading=!0,ag}}},function(...a){let b=(()=>{let a=(0,g.useContext)(S);return(0,g.useMemo)(()=>o(Q,a),[a])})(),[c,e,f]="function"==typeof a[1]?[a[0],a[1],a[2]||{}]:[a[0],null,(null===a[1]?a[2]:a[1])||{}],h=R(b,f),i=d,{use:j}=h,k=(j||[]).concat(U);for(let a=k.length;a--;)i=k[a](i);return i(c,e||h.fetcher||null,h)});var _=c(5478),aa=c(4686);class ab{async getInvoices(a={}){try{let b=new URLSearchParams;void 0!==a.skip&&b.append("skip",a.skip.toString()),void 0!==a.limit&&b.append("limit",a.limit.toString());let c=`${aa.R7.LIST}?${b.toString()}`,d=await _.uE.get(c);return(0,_.sM)(d)}catch(a){throw(0,_.hS)(a)}}async getInvoiceById(a){try{let b=aa.R7.BY_ID(a),c=await _.uE.get(b);return(0,_.sM)(c)}catch(a){throw(0,_.hS)(a)}}async createInvoice(a){try{let b=await _.uE.post(aa.R7.CREATE,a);return(0,_.sM)(b)}catch(a){throw(0,_.hS)(a)}}async downloadInvoice(a){try{let b=aa.W2.DOWNLOAD_INVOICE(a),c=await fetch(`${_.uE.baseURL}${b}`,{credentials:"include"});if(!c.ok)throw Error(`Failed to download invoice: ${c.statusText}`);return await c.blob()}catch(a){throw(0,_.hS)(a)}}formatCurrency(a){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(a)}formatDate(a){return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(new Date(a))}getStatusColor(a){switch(a){case"PAID":return"text-green-600 bg-green-100";case"PENDING":return"text-yellow-600 bg-yellow-100";case"OVERDUE":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}}isOverdue(a){return new Date(a)<new Date&&new Date(a).toDateString()!==new Date().toDateString()}calculateDaysUntilDue(a){let b=new Date(a),c=new Date;return Math.ceil((b.getTime()-c.getTime())/864e5)}}let ac=new ab,ad=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},ae=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var af={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let ag=(0,g.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:d,className:e="",children:f,iconNode:h,...i},j)=>(0,g.createElement)("svg",{ref:j,...af,width:b,height:b,stroke:a,strokeWidth:d?24*Number(c)/Number(b):c,className:ae("lucide",e),...!f&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(i)&&{"aria-hidden":"true"},...i},[...h.map(([a,b])=>(0,g.createElement)(a,b)),...Array.isArray(f)?f:[f]])),ah=(a,b)=>{let c=(0,g.forwardRef)(({className:c,...d},e)=>(0,g.createElement)(ag,{ref:e,iconNode:b,className:ae(`lucide-${ad(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...d}));return c.displayName=ad(a),c},ai=ah("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),aj=ah("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),ak=ah("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),al=ah("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);function am({invoice:a,onView:b}){let c=async()=>{try{let b=await ac.downloadInvoice(a.invoice_file_name),c=window.URL.createObjectURL(b),d=document.createElement("a");d.href=c,d.download=a.invoice_file_name,document.body.appendChild(d),d.click(),window.URL.revokeObjectURL(c),document.body.removeChild(d)}catch(a){console.error("Download failed:",a),alert("Failed to download invoice")}},d=ac.getStatusColor(a.status),f=ac.isOverdue(a.due_date),g=ac.calculateDaysUntilDue(a.due_date);return(0,e.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg border border-gray-200 hover:shadow-md transition-shadow",children:(0,e.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,e.jsx)("div",{className:"flex items-center justify-between",children:(0,e.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,e.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,e.jsx)("h3",{className:"text-lg font-medium text-gray-900 truncate",children:a.invoice_number}),(0,e.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${d}`,children:a.status})]}),(0,e.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["Client: ",(0,e.jsx)("span",{className:"font-medium",children:a.client_name})]}),(0,e.jsxs)("div",{className:"flex items-center text-sm text-gray-500 mb-2",children:[(0,e.jsx)(ai,{className:"h-4 w-4 mr-1"}),(0,e.jsx)("span",{className:"font-medium text-gray-900",children:ac.formatCurrency(a.amount)})]}),(0,e.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[(0,e.jsx)(aj,{className:"h-4 w-4 mr-1"}),(0,e.jsxs)("span",{children:["Due: ",ac.formatDate(a.due_date)]}),f&&(0,e.jsx)("span",{className:"ml-2 text-red-600 font-medium",children:"(Overdue)"}),!f&&g<=7&&g>0&&(0,e.jsxs)("span",{className:"ml-2 text-yellow-600 font-medium",children:["(",g," days left)"]})]})]})}),(0,e.jsxs)("div",{className:"mt-4 flex space-x-2",children:[(0,e.jsxs)("button",{onClick:()=>b?.(a.id),className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[(0,e.jsx)(ak,{className:"h-4 w-4 mr-1"}),"View Details"]}),(0,e.jsxs)("button",{onClick:c,className:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",children:[(0,e.jsx)(al,{className:"h-4 w-4 mr-1"}),"Download PDF"]})]})]})})}function an({size:a="md",className:b=""}){return(0,e.jsx)("div",{className:`animate-spin rounded-full border-2 border-gray-300 border-t-indigo-600 ${{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12"}[a]} ${b}`})}function ao(){let{user:a,logout:b}=(0,f.A)(),[c,d]=(0,g.useState)(0),{invoices:h,isLoading:i,error:j,pagination:k}=function(a={}){let{skip:b=0,limit:c=5,refreshInterval:d=0}=a,e=async()=>{let a=await ac.getInvoices({skip:b,limit:c});if(200!==a.status)throw Error(a.message||"Failed to fetch invoices");return a},{data:f,error:g,isLoading:h,mutate:i}=$(`/api/invoices?skip=${b}&limit=${c}`,e,{refreshInterval:d,revalidateOnFocus:!1,revalidateOnReconnect:!0,dedupingInterval:5e3});return{invoices:f?.data||[],pagination:f?.pagination,isLoading:h,error:g,mutate:i}}({skip:5*c,limit:5});return i?(0,e.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,e.jsx)(an,{size:"lg"})}):j?(0,e.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,e.jsxs)("div",{className:"text-center",children:[(0,e.jsx)("h2",{className:"text-2xl font-bold text-red-600 mb-4",children:"Error"}),(0,e.jsx)("p",{className:"text-gray-600",children:j.message})]})}):(0,e.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,e.jsx)("header",{className:"bg-white shadow",children:(0,e.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,e.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,e.jsxs)("div",{children:[(0,e.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),(0,e.jsxs)("p",{className:"text-gray-600",children:["Welcome back, ",a?.first_name,"!"]})]}),(0,e.jsx)("button",{onClick:b,className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Logout"})]})})}),(0,e.jsx)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,e.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,e.jsxs)("div",{className:"mb-6",children:[(0,e.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Recent Invoices"}),0===h.length?(0,e.jsx)("div",{className:"text-center py-12",children:(0,e.jsx)("p",{className:"text-gray-500",children:"No invoices found."})}):(0,e.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:h.map(a=>(0,e.jsx)(am,{invoice:a,onView:a=>{console.log("View invoice:",a)}},a.id))})]}),k&&k.total>5&&(0,e.jsx)("div",{className:"flex justify-center mt-8",children:(0,e.jsxs)("nav",{className:"flex space-x-2",children:[(0,e.jsx)("button",{onClick:()=>d(Math.max(0,c-1)),disabled:0===c,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,e.jsxs)("span",{className:"px-3 py-2 text-sm font-medium text-gray-700 bg-gray-50 border border-gray-300 rounded-md",children:["Page ",c+1," of ",Math.ceil(k.total/5)]}),(0,e.jsx)("button",{onClick:()=>d(c+1),disabled:(c+1)*5>=k.total,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})})]})})]})}},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},7488:(a,b,c)=>{Promise.resolve().then(c.bind(c,3814))},8112:(a,b,c)=>{Promise.resolve().then(c.bind(c,1236))},8354:a=>{"use strict";a.exports=require("util")},9088:(a,b,c)=>{"use strict";a.exports=c(2797)},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9902:a=>{"use strict";a.exports=require("path")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[586,352,44],()=>b(b.s=3042));module.exports=c})();