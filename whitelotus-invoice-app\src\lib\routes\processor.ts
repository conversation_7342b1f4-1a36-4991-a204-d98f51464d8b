import { NextRequest, NextResponse } from 'next/server';
import { RouteMatcher } from './matcher';
import { RouteContext, RouteConfig, RouteMiddleware } from './types';
import { getCurrentUserFromRequest } from '@/lib/auth';
import { getSecurityHeaders, checkRateLimit } from '@/lib/security';

/**
 * Main route processor that handles all API requests
 */
export class RouteProcessor {
  private config: RouteConfig;
  private globalMiddlewares: RouteMiddleware[];

  constructor(config: RouteConfig = {}) {
    this.config = {
      enableLogging: true,
      defaultRateLimit: {
        requests: 100,
        windowMs: 60000, // 1 minute
      },
      defaultHeaders: {},
      ...config,
    };
    this.globalMiddlewares = config.globalMiddlewares || [];
  }

  /**
   * Process an incoming API request
   */
  async processRequest(request: NextRequest): Promise<NextResponse> {
    const startTime = Date.now();
    const method = request.method;
    const url = new URL(request.url);
    const pathname = url.pathname.replace('/api', ''); // Remove /api prefix

    try {
      // Log request if enabled
      if (this.config.enableLogging) {
        console.log(`[API] ${method} ${pathname} - ${request.headers.get('user-agent') || 'Unknown'}`);
      }

      // Find matching route
      const routeMatch = RouteMatcher.match(method, pathname);

      if (!routeMatch) {
        return this.createErrorResponse(404, 'Route not found');
      }

      const { definition, params } = routeMatch;

      // Create route context
      const context: RouteContext = {
        params,
        searchParams: url.searchParams,
      };

      // Check authentication if required
      if (definition.requiresAuth) {
        const user = await getCurrentUserFromRequest(request);
        if (!user) {
          return this.createErrorResponse(401, 'Unauthorized');
        }
        context.user = user;
      }

      // Apply rate limiting
      if (definition.rateLimit) {
        const clientIP = request.headers.get('x-forwarded-for') ||
          request.headers.get('x-real-ip') ||
          'unknown';

        const rateLimitKey = definition.rateLimit.keyGenerator
          ? definition.rateLimit.keyGenerator(request)
          : `${pathname}:${clientIP}`;

        if (!checkRateLimit(
          rateLimitKey,
          definition.rateLimit.requests,
          definition.rateLimit.windowMs
        )) {
          return this.createErrorResponse(429, 'Too many requests');
        }
      }

      // Execute global middlewares
      let response: NextResponse | null = null;
      let middlewareIndex = 0;

      const executeNextMiddleware = async (): Promise<NextResponse> => {
        if (middlewareIndex < this.globalMiddlewares.length) {
          const middleware = this.globalMiddlewares[middlewareIndex++];
          return await middleware(request, context, executeNextMiddleware);
        } else {
          // Execute the route handler
          return await definition.handler(request, context);
        }
      };

      response = await executeNextMiddleware();

      // Log response if enabled
      if (this.config.enableLogging) {
        const duration = Date.now() - startTime;
        console.log(`[API] ${method} ${pathname} - ${response.status} (${duration}ms)`);
      }

      return response;

    } catch (error) {
      console.error(`[API Error] ${method} ${pathname}:`, error);

      if (this.config.enableLogging) {
        const duration = Date.now() - startTime;
        console.log(`[API] ${method} ${pathname} - 500 (${duration}ms)`);
      }

      return this.createErrorResponse(500, 'Internal server error');
    }
  }

  /**
   * Create a standardized error response
   */
  private createErrorResponse(status: number, message: string): NextResponse {
    return NextResponse.json(
      { status, message },
      {
        status,
        headers: {
          ...getSecurityHeaders(),
          ...this.config.defaultHeaders,
        }
      }
    );
  }

  /**
   * Add a global middleware
   */
  addGlobalMiddleware(middleware: RouteMiddleware): void {
    this.globalMiddlewares.push(middleware);
  }

  /**
   * Remove a global middleware
   */
  removeGlobalMiddleware(middleware: RouteMiddleware): void {
    const index = this.globalMiddlewares.indexOf(middleware);
    if (index > -1) {
      this.globalMiddlewares.splice(index, 1);
    }
  }

  /**
   * Get route information for debugging
   */
  getRouteInfo(method: string, pathname: string) {
    const routeMatch = RouteMatcher.match(method, pathname);
    return routeMatch ? {
      path: routeMatch.definition.path,
      method: routeMatch.definition.method,
      requiresAuth: routeMatch.definition.requiresAuth,
      rateLimit: routeMatch.definition.rateLimit,
      description: routeMatch.definition.description,
      tags: routeMatch.definition.tags,
      params: routeMatch.params,
    } : null;
  }
}

// Create a default processor instance
export const defaultRouteProcessor = new RouteProcessor({
  enableLogging: process.env.NODE_ENV === 'development',
  defaultHeaders: {
    'X-API-Version': '1.0.0',
  },
});
