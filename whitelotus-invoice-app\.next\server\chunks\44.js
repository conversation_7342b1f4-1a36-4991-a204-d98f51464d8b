exports.id=44,exports.ids=[44],exports.modules={1135:()=>{},1447:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>e});var d=c(7954);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Workspace\\Whitelotus assignment\\whitelotus-invoice-app\\src\\context\\AuthContext.tsx","AuthProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\Workspace\\Whitelotus assignment\\whitelotus-invoice-app\\src\\context\\AuthContext.tsx","useAuth")},1472:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g,metadata:()=>f});var d=c(5338),e=c(1447);c(1135);let f={title:"WhiteLotus Invoice App",description:"Secure invoice management system with CSRF protection"};function g({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{children:(0,d.jsx)(e.AuthProvider,{children:a})})})}},2111:(a,b,c)=>{Promise.resolve().then(c.bind(c,2278))},2278:(a,b,c)=>{"use strict";c.d(b,{AuthProvider:()=>l,A:()=>m});var d=c(1124),e=c(8301),f=c(2378),g=c(5478),h=c(4686);class i{async login(a){try{let b=await g.uE.post(h.mM.LOGIN,a);return(0,g.sM)(b)}catch(a){throw(0,g.hS)(a)}}logout(){}validateCredentials(a,b){let c=[];return a?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a)||c.push("Please enter a valid email address"):c.push("Email is required"),b?b.length<8&&c.push("Password must be at least 8 characters long"):c.push("Password is required"),c}isTokenExpired(a){try{let b=JSON.parse(atob(a.split(".")[1])),c=Date.now()/1e3;return b.exp<c}catch(a){return!0}}}let j=new i,k=(0,e.createContext)(void 0);function l({children:a}){let[b,c]=(0,e.useState)(null),[g,i]=(0,e.useState)(!0),l=(0,f.useRouter)(),m=async(a,b)=>{i(!0);try{let d=await j.login({email:a,password:b});if(200===d.status&&d.data)c(d.data),l.push(h.bw.DASHBOARD);else throw Error(d.message||"Login failed")}catch(a){throw console.error("Login failed:",a),a}finally{i(!1)}};return(0,d.jsx)(k.Provider,{value:{user:b,login:m,logout:()=>{i(!0);try{j.logout(),c(null),l.push(h.bw.LOGIN)}finally{i(!1)}},loading:g,isAuthenticated:!!b},children:a})}function m(){let a=(0,e.useContext)(k);if(void 0===a)throw Error("useAuth must be used within an AuthProvider");return a}},2468:(a,b,c)=>{"use strict";c.d(b,{Hi:()=>g}),c(255);class d extends Error{constructor(a,b="CSRF_ERROR",c=403,e=!0){super(a),this.name="CSRFError",this.code=b,this.statusCode=c,this.isRetryable=e,Error.captureStackTrace&&Error.captureStackTrace(this,d)}static isCSRFError(a){return a instanceof d}static fromApiResponse(a){let b=a.message.toLowerCase().includes("csrf")||a.message.toLowerCase().includes("token");return 403===a.status&&b?new d(a.message,"CSRF_TOKEN_INVALID",403,!0):new d(a.message||"CSRF validation failed","CSRF_VALIDATION_FAILED",a.status,!0)}getUserMessage(){switch(this.code){case"CSRF_TOKEN_MISSING":return"Security token is missing. Please refresh the page and try again.";case"CSRF_TOKEN_INVALID":return"Security token is invalid. Please refresh the page and try again.";case"CSRF_TOKEN_EXPIRED":return"Security token has expired. Please refresh the page and try again.";case"CSRF_FETCH_FAILED":return"Failed to obtain security token. Please check your connection and try again.";default:return"Security validation failed. Please refresh the page and try again."}}getSuggestedAction(){return this.isRetryable?"Please refresh the page and try again.":"Please contact support if this problem persists."}}let e={FETCH_FAILED:"CSRF_FETCH_FAILED",VALIDATION_FAILED:"CSRF_VALIDATION_FAILED"};class f{async getToken(){if(this.token&&Date.now()<this.tokenExpiry)return this.token;if(this.fetchPromise)return this.fetchPromise;this.fetchPromise=this.fetchToken();try{return await this.fetchPromise}finally{this.fetchPromise=null}}async fetchToken(){try{let a=await fetch("/api/csrf-token",{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});if(!a.ok){let b=await a.json().catch(()=>({}));throw new d(b.message||`Failed to fetch CSRF token: ${a.status}`,e.FETCH_FAILED,a.status)}let b=await a.json();if(200===b.status&&b.data?.csrfToken)return this.token=b.data.csrfToken,this.tokenExpiry=Date.now()+3e6,this.token;throw new d("Invalid CSRF token response",e.VALIDATION_FAILED)}catch(a){if(a instanceof d)throw a;throw console.error("Failed to fetch CSRF token:",a),new d("Network error while fetching CSRF token",e.FETCH_FAILED)}}clearToken(){this.token=null,this.tokenExpiry=0}isTokenExpired(){return!this.token||Date.now()>=this.tokenExpiry}constructor(){this.token=null,this.tokenExpiry=0,this.fetchPromise=null}}let g=new f},4686:(a,b,c)=>{"use strict";c.d(b,{R7:()=>g,W2:()=>h,bw:()=>e,i3:()=>d,mM:()=>f});let d={BASE_URL:"http://localhost:3001/api",TIMEOUT:parseInt(process.env.API_TIMEOUT||"10000",10),RETRIES:parseInt(process.env.API_RETRIES||"3",10)};process.env.JWT_SECRET||console.warn("⚠️  WARNING: Using default JWT_SECRET. Set JWT_SECRET environment variable in production!"),process.env.COOKIE_NAME,parseInt(process.env.COOKIE_MAX_AGE||"604800",10),parseInt(process.env.PAGINATION_DEFAULT_LIMIT||"5",10),parseInt(process.env.PAGINATION_MAX_LIMIT||"100",10),parseInt(process.env.ISR_REVALIDATE||"60",10),parseInt(process.env.RATE_LIMIT_LOGIN_ATTEMPTS||"5",10),parseInt(process.env.RATE_LIMIT_LOGIN_WINDOW||"60000",10),parseInt(process.env.RATE_LIMIT_DOWNLOAD_ATTEMPTS||"20",10),parseInt(process.env.RATE_LIMIT_DOWNLOAD_WINDOW||"60000",10);let e={HOME:"/",LOGIN:"/login",DASHBOARD:"/dashboard",INVOICE_DETAILS:"/invoice",CREATE_INVOICE:"/invoice/new"},f={LOGIN:"/auth/login",LOGOUT:"/auth/logout",ME:"/auth/me"},g={LIST:"/invoices",CREATE:"/invoices",BY_ID:a=>`/invoices/${a}`},h={DOWNLOAD_INVOICE:a=>`/invoices/download/${a}`};process.env.ADMIN_EMAIL||console.warn("⚠️  WARNING: Using default admin email. Set ADMIN_EMAIL environment variable!"),process.env.ADMIN_PASSWORD||console.warn("⚠️  WARNING: Using default admin password. Set ADMIN_PASSWORD environment variable!"),process.env.DEBUG_LOGGING,parseInt(process.env.PORT||"3000",10)},4907:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4160,23)),Promise.resolve().then(c.t.bind(c,1603,23)),Promise.resolve().then(c.t.bind(c,8495,23)),Promise.resolve().then(c.t.bind(c,5170,23)),Promise.resolve().then(c.t.bind(c,7526,23)),Promise.resolve().then(c.t.bind(c,8922,23)),Promise.resolve().then(c.t.bind(c,9234,23)),Promise.resolve().then(c.t.bind(c,2263,23)),Promise.resolve().then(c.bind(c,2146))},5478:(a,b,c)=>{"use strict";c.d(b,{hS:()=>h,sM:()=>i,uE:()=>g});var d=c(4686),e=c(2468);class f{constructor(){this.baseURL=d.i3.BASE_URL,this.timeout=d.i3.TIMEOUT}async request(a,b={}){let c=`${this.baseURL}${a}`,d={"Content-Type":"application/json",...b.headers};if(b.method&&!["GET","HEAD","OPTIONS"].includes(b.method.toUpperCase()))try{let a=await e.Hi.getToken();d["x-csrf-token"]=a}catch(a){console.warn("Failed to get CSRF token:",a)}let f={...b,headers:d,credentials:"include"};try{let a=new AbortController,b=setTimeout(()=>a.abort(),this.timeout),d=await fetch(c,{...f,signal:a.signal});if(clearTimeout(b),!d.ok){let a=await d.json().catch(()=>({}));throw 403===d.status&&a.message?.includes("CSRF")&&(console.warn("CSRF token error, clearing token cache"),e.Hi.clearToken()),Error(a.message||`HTTP ${d.status}: ${d.statusText}`)}return await d.json()}catch(a){if(a instanceof Error){if("AbortError"===a.name)throw Error("Request timeout");throw a}throw Error("An unexpected error occurred")}}async get(a,b){return this.request(a,{method:"GET",headers:b})}async post(a,b,c){return this.request(a,{method:"POST",body:b?JSON.stringify(b):void 0,headers:c})}async put(a,b,c){return this.request(a,{method:"PUT",body:b?JSON.stringify(b):void 0,headers:c})}async delete(a,b){return this.request(a,{method:"DELETE",headers:b})}}let g=new f;function h(a){return a instanceof Error?{message:a.message,code:"API_ERROR"}:{message:"An unexpected error occurred",code:"UNKNOWN_ERROR"}}function i(a){if(!a||"object"!=typeof a)throw Error("Invalid API response format");if("number"!=typeof a.status)throw Error("Invalid API response: missing status");if("string"!=typeof a.message)throw Error("Invalid API response: missing message");return a}},7383:(a,b,c)=>{Promise.resolve().then(c.bind(c,1447))},7955:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,1170,23)),Promise.resolve().then(c.t.bind(c,3597,23)),Promise.resolve().then(c.t.bind(c,6893,23)),Promise.resolve().then(c.t.bind(c,9748,23)),Promise.resolve().then(c.t.bind(c,6060,23)),Promise.resolve().then(c.t.bind(c,7184,23)),Promise.resolve().then(c.t.bind(c,9576,23)),Promise.resolve().then(c.t.bind(c,3041,23)),Promise.resolve().then(c.t.bind(c,1384,23))}};