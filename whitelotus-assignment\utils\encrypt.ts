import Cryptr from 'cryptr';

const cryptr = new Cryptr(process.env.ENCRYPTION_KEY || 'default-encryption-key');

export function encryptBody(text: string): string {
    try {
        return cryptr.encrypt(text);
    } catch (error) {
        console.error('Encryption error:', error);
        throw new Error('Failed to encrypt data');
    }
}

export function decryptBody(encryptedText: string): string {
    try {
        return cryptr.decrypt(encryptedText);
    } catch (error) {
        console.error('Decryption error:', error);
        throw new Error('Failed to decrypt data');
    }
}
