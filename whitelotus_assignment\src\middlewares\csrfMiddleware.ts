import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { httpStatusCodes } from '../utils/constants';
import { logger } from '../utils/logger';
import { getCSRFConfig } from '../config/csrfConfig';

// In-memory store for CSRF tokens (in production, use Redis or database)
const csrfTokenStore = new Map<string, { token: string; expires: number; userId?: string }>();

// Get CSRF configuration
const CSRF_CONFIG = getCSRFConfig();

/**
 * Generate a cryptographically secure CSRF token
 */
export function generateCSRFToken(): string {
    return crypto.randomBytes(CSRF_CONFIG.tokenLength).toString('hex');
}

/**
 * Create a session key for storing CSRF token
 */
function createSessionKey(req: Request): string {
    // Use user ID if available, otherwise use IP + User-Agent
    const userId = req['id'] || req['user']?.id;
    if (userId) {
        return `user:${userId}`;
    }

    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    return `session:${crypto
        .createHash('sha256')
        .update(ip + userAgent)
        .digest('hex')}`;
}

/**
 * Store CSRF token with expiration
 */
function storeCSRFToken(sessionKey: string, token: string, userId?: string): void {
    const expires = Date.now() + CSRF_CONFIG.tokenExpiry;
    csrfTokenStore.set(sessionKey, { token, expires, userId });
}

/**
 * Retrieve and validate stored CSRF token
 */
function getStoredCSRFToken(sessionKey: string): string | null {
    const stored = csrfTokenStore.get(sessionKey);
    if (!stored) {
        return null;
    }

    // Check if token has expired
    if (Date.now() > stored.expires) {
        csrfTokenStore.delete(sessionKey);
        return null;
    }

    return stored.token;
}

/**
 * Clean up expired tokens
 */
function cleanupExpiredTokens(): void {
    const now = Date.now();
    for (const [key, value] of csrfTokenStore.entries()) {
        if (now > value.expires) {
            csrfTokenStore.delete(key);
        }
    }
}

// Set up periodic cleanup
setInterval(cleanupExpiredTokens, CSRF_CONFIG.cleanupInterval);

/**
 * Middleware to generate and provide CSRF token
 */
export function generateCSRFMiddleware(req: Request, res: Response, next: NextFunction): void {
    try {
        const sessionKey = createSessionKey(req);

        // Check if we already have a valid token
        let token = getStoredCSRFToken(sessionKey);

        if (!token) {
            // Generate new token
            token = generateCSRFToken();
            const userId = req['id'] || req['user']?.id;
            storeCSRFToken(sessionKey, token, userId);
        }

        // Add token to response locals for access in routes
        res.locals.csrfToken = token;

        // Set CSRF token in cookie (httpOnly for security)
        res.cookie(CSRF_CONFIG.cookieName, token, {
            httpOnly: CSRF_CONFIG.httpOnly,
            secure: CSRF_CONFIG.secureCookies,
            sameSite: CSRF_CONFIG.sameSite,
            maxAge: CSRF_CONFIG.tokenExpiry
        });

        next();
    } catch (error) {
        logger.error('CSRF token generation error:', error);
        res.status(httpStatusCodes.SERVER_ERROR_CODE).json({
            status: httpStatusCodes.SERVER_ERROR_CODE,
            message: 'Failed to generate CSRF token'
        });
    }
}

/**
 * Middleware to validate CSRF token for state-changing operations
 */
export function validateCSRFMiddleware(req: Request, res: Response, next: NextFunction): void {
    try {
        // Skip CSRF validation for configured methods
        if (CSRF_CONFIG.skipMethods.includes(req.method)) {
            return next();
        }

        const sessionKey = createSessionKey(req);
        const storedToken = getStoredCSRFToken(sessionKey);

        if (!storedToken) {
            logger.warn(`CSRF validation failed: No stored token for session ${sessionKey}`);
            res.status(httpStatusCodes.FORBIDDEN_CODE).json({
                status: httpStatusCodes.FORBIDDEN_CODE,
                message: 'CSRF token not found. Please refresh and try again.'
            });
            return;
        }

        // Get token from header or body
        const providedToken = req.get(CSRF_CONFIG.headerName) || req.body.csrfToken;

        if (!providedToken) {
            logger.warn(`CSRF validation failed: No token provided in request`);
            res.status(httpStatusCodes.FORBIDDEN_CODE).json({
                status: httpStatusCodes.FORBIDDEN_CODE,
                message: 'CSRF token required'
            });
            return;
        }

        // Use timing-safe comparison to prevent timing attacks
        if (!crypto.timingSafeEqual(Buffer.from(storedToken), Buffer.from(providedToken))) {
            logger.warn(`CSRF validation failed: Token mismatch for session ${sessionKey}`);
            res.status(httpStatusCodes.FORBIDDEN_CODE).json({
                status: httpStatusCodes.FORBIDDEN_CODE,
                message: 'Invalid CSRF token'
            });
            return;
        }

        // Token is valid, proceed
        next();
    } catch (error) {
        logger.error('CSRF validation error:', error);
        res.status(httpStatusCodes.SERVER_ERROR_CODE).json({
            status: httpStatusCodes.SERVER_ERROR_CODE,
            message: 'CSRF validation failed'
        });
    }
}

/**
 * Get CSRF token for current session
 */
export function getCSRFToken(req: Request): string | null {
    const sessionKey = createSessionKey(req);
    return getStoredCSRFToken(sessionKey);
}

/**
 * Invalidate CSRF token for current session
 */
export function invalidateCSRFToken(req: Request): void {
    const sessionKey = createSessionKey(req);
    csrfTokenStore.delete(sessionKey);
}

export default {
    generateCSRFMiddleware,
    validateCSRFMiddleware,
    getCSRFToken,
    invalidateCSRFToken,
    generateCSRFToken
};
