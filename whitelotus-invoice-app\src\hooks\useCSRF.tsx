'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { csrfTokenManager } from '@/lib/security';

interface UseCSRFReturn {
  token: string | null;
  isLoading: boolean;
  error: string | null;
  refreshToken: () => Promise<void>;
  clearToken: () => void;
}

/**
 * Hook for managing CSRF tokens in React components
 */
export function useCSRF(): UseCSRFReturn {
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch CSRF token
   */
  const fetchToken = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const csrfToken = await csrfTokenManager.getToken();
      setToken(csrfToken);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch CSRF token';
      setError(errorMessage);
      console.error('CSRF token fetch error:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Refresh token manually
   */
  const refreshToken = useCallback(async () => {
    csrfTokenManager.clearToken();
    await fetchToken();
  }, [fetchToken]);

  /**
   * Clear token
   */
  const clearToken = useCallback(() => {
    csrfTokenManager.clearToken();
    setToken(null);
    setError(null);
  }, []);

  // Fetch token on mount
  useEffect(() => {
    fetchToken();
  }, [fetchToken]);

  return {
    token,
    isLoading,
    error,
    refreshToken,
    clearToken,
  };
}

/**
 * Hook for getting CSRF token for forms
 */
export function useCSRFToken(): string | null {
  const { token } = useCSRF();
  return token;
}

/**
 * Higher-order component to provide CSRF protection
 */
export function withCSRFProtection<T extends object>(
  Component: React.ComponentType<T>
): React.ComponentType<T> {
  return function CSRFProtectedComponent(props: T) {
    const { token, isLoading, error } = useCSRF();

    if (isLoading) {
      return <div>Loading security token...</div>;
    }

    if (error) {
      return <div>Security error: { error } </div>;
    }

    if (!token) {
      return <div>Security token not available </div>;
    }

    return <Component { ...props } />;
  };
}
