# WhiteLotus Invoice App - Render Deployment Guide

## Overview
This guide will help you deploy your WhiteLotus Invoice application to Render using their free tier. The deployment includes:
- PostgreSQL Database (Free tier - 30 days)
- Backend API (Node.js/Express)
- Frontend (Next.js)

## Prerequisites
1. GitHub repository with your code
2. Render account (free)
3. Payment method added to Render (required even for free tier)

## Deployment Status
✅ PostgreSQL Database: `whitelotus-invoice-db` (ID: dpg-d35e9l6uk2gs73bffqh0-a)
✅ Frontend Static Site: `whitelotus-frontend` (https://whitelotus-frontend.onrender.com)
⚠️  Backend API: Requires payment method setup

## Database Connection Details
- **Database Name**: whitelotus_invoice_db
- **Database User**: whitelotus_invoice_db_user
- **Host**: dpg-d35e9l6uk2gs73bffqh0-a
- **Port**: 5432
- **Connection URL**: Available in Render dashboard

## Step-by-Step Deployment

### 1. Add Payment Method to Render
Even for free tier services, Render requires a payment method:
1. Go to https://dashboard.render.com/billing
2. Add a credit/debit card
3. Note: Free tier services won't charge your card

### 2. Deploy Backend API

#### Option A: Using Render Dashboard
1. Go to https://dashboard.render.com/web/new
2. Connect your GitHub repository
3. Configure the service:
   - **Name**: whitelotus-backend-api
   - **Runtime**: Node
   - **Build Command**: `cd whitelotus_assignment && npm install && npm run build`
   - **Start Command**: `cd whitelotus_assignment && npm start`
   - **Plan**: Free
   - **Region**: Oregon

#### Option B: Using Docker (Requires paid plan)
The Dockerfile has been created at `whitelotus_assignment/Dockerfile`

### 3. Configure Environment Variables

#### Backend Environment Variables
Set these in your Render service dashboard:

```bash
NODE_ENV=production
PORT=10000
DATABASE_NAME=whitelotus_invoice_db
DATABASE_USER=whitelotus_invoice_db_user
DATABASE_HOST=dpg-d35e9l6uk2gs73bffqh0-a
DATABASE_PORT=5432
DATABASE_PASS=[Get from Render dashboard]
ENCRYPTION_KEY=your-super-secret-encryption-key-change-this
MEDIA_LINK=https://your-backend-service.onrender.com/
SERVER_URL=https://your-backend-service.onrender.com/
USESERVER=true
MODE=production
```

#### Frontend Environment Variables
Update the static site with:

```bash
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://your-backend-service.onrender.com
```

### 4. Update Frontend Configuration

The frontend static site needs the correct publish path. Update in Render dashboard:
- **Publish Directory**: `whitelotus-invoice-app/out` (for static export)

Or deploy as a web service instead:
- **Build Command**: `cd whitelotus-invoice-app && npm install && npm run build`
- **Start Command**: `cd whitelotus-invoice-app && npm start`

### 5. Database Setup

The PostgreSQL database is already created. You'll need to:
1. Get the database password from Render dashboard
2. Run any necessary database migrations
3. Seed initial data if needed

## Important Notes

### Free Tier Limitations
- **PostgreSQL**: 30-day limit, then requires paid plan
- **Web Services**: Sleep after 15 minutes of inactivity
- **Static Sites**: No limitations on free tier

### Security Considerations
- Change all default passwords and secrets
- Use environment variables for sensitive data
- Enable HTTPS (automatic on Render)

### Performance Optimization
- Free tier services have limited resources
- Consider upgrading to paid plans for production use
- Monitor usage and performance metrics

## Troubleshooting

### Common Issues
1. **Build Failures**: Check build logs in Render dashboard
2. **Database Connection**: Verify environment variables
3. **CORS Issues**: Update CORS settings in backend
4. **Static Site 404s**: Check publish directory path

### Health Checks
- Backend health endpoint: `/health`
- Database connection test available in backend

## Next Steps
1. Add payment method to Render account
2. Deploy backend service
3. Update environment variables
4. Test the complete application
5. Set up monitoring and logging

## Support
- Render Documentation: https://render.com/docs
- Community Forum: https://community.render.com
- Support Email: <EMAIL>
