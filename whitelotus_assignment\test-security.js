const http = require('http');

// Test function to make HTTP requests
function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    body: body
                });
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(data);
        }
        req.end();
    });
}

async function testSecurityHeaders() {
    console.log('Testing Security Headers...\n');
    
    try {
        // Test basic endpoint to check security headers
        const response = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api-docs',
            method: 'GET'
        });

        console.log('Status Code:', response.statusCode);
        console.log('\nSecurity Headers:');
        console.log('X-Content-Type-Options:', response.headers['x-content-type-options']);
        console.log('X-Frame-Options:', response.headers['x-frame-options']);
        console.log('X-XSS-Protection:', response.headers['x-xss-protection']);
        console.log('Strict-Transport-Security:', response.headers['strict-transport-security']);
        console.log('Content-Security-Policy:', response.headers['content-security-policy']);
        console.log('X-Powered-By:', response.headers['x-powered-by'] || 'Not present (Good!)');
        
    } catch (error) {
        console.error('Error testing security headers:', error.message);
    }
}

async function testRateLimit() {
    console.log('\n\nTesting Rate Limiting...\n');
    
    try {
        // Test login rate limiting by making multiple requests
        const loginData = JSON.stringify({
            email: '<EMAIL>',
            password: 'testpassword'
        });

        console.log('Making 6 login requests to test rate limiting...');
        
        for (let i = 1; i <= 6; i++) {
            try {
                const response = await makeRequest({
                    hostname: 'localhost',
                    port: 3000,
                    path: '/api/login',
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Content-Length': Buffer.byteLength(loginData)
                    }
                }, loginData);

                console.log(`Request ${i}: Status ${response.statusCode}`);
                
                if (response.statusCode === 429) {
                    console.log('Rate limit triggered! Response:', JSON.parse(response.body));
                    break;
                }
                
                // Add small delay between requests
                await new Promise(resolve => setTimeout(resolve, 100));
                
            } catch (error) {
                console.log(`Request ${i}: Error -`, error.message);
            }
        }
        
    } catch (error) {
        console.error('Error testing rate limiting:', error.message);
    }
}

async function testGeneralRateLimit() {
    console.log('\n\nTesting General Rate Limiting...\n');
    
    try {
        console.log('Making multiple requests to test general rate limiting...');
        
        for (let i = 1; i <= 5; i++) {
            try {
                const response = await makeRequest({
                    hostname: 'localhost',
                    port: 3000,
                    path: '/api-docs',
                    method: 'GET'
                });

                console.log(`Request ${i}: Status ${response.statusCode}`);
                console.log('Rate Limit Headers:');
                console.log('  X-RateLimit-Limit:', response.headers['x-ratelimit-limit']);
                console.log('  X-RateLimit-Remaining:', response.headers['x-ratelimit-remaining']);
                console.log('  X-RateLimit-Reset:', response.headers['x-ratelimit-reset']);
                
                // Add small delay between requests
                await new Promise(resolve => setTimeout(resolve, 100));
                
            } catch (error) {
                console.log(`Request ${i}: Error -`, error.message);
            }
        }
        
    } catch (error) {
        console.error('Error testing general rate limiting:', error.message);
    }
}

// Run tests
async function runTests() {
    console.log('='.repeat(50));
    console.log('SECURITY FEATURES TEST');
    console.log('='.repeat(50));
    
    await testSecurityHeaders();
    await testRateLimit();
    await testGeneralRateLimit();
    
    console.log('\n' + '='.repeat(50));
    console.log('TESTS COMPLETED');
    console.log('='.repeat(50));
}

// Check if server is running before running tests
setTimeout(() => {
    runTests().catch(console.error);
}, 2000); // Wait 2 seconds for server to start
